<?php
  /**
   * Definier hier de koppeling tussen pageId en url/module/action
   * 'def' = het unieke url gedeelte (verplicht)
   * 'name' = leesbare naam. Vetaling word automatisch opgezocht. (niet-verplicht, wel beter voor performance)
   * 'module' = welke module te laden
   * 'action' = welke actie te laden (default)
   * 'params' = welke parameters mee te nemen bij het bouwen van de url
   * 'target' = bij klik op link waar openen (_blank)
   */

  return [
    'M_LOGIN'                 => [
      'def'    => 'inloggen',
      'name'   => 'Inloggen',
      'module' => 'login',
      'action' => 'login',
    ],
    'M_LOGOFF'                => [
      'def'    => 'uitloggen',
      'name'   => 'Uitloggen',
      'module' => 'login',
      'action' => 'logout',
    ],
    'M_GOOGLEAUTHENTICATOR'   => [
      'def'    => 'googleauthenticator',
      'name'   => 'Googleauthenticator',
      'module' => 'login',
      'action' => 'googleauthenticator',
    ],
    'M_LOGIN_VALIDATE_IPADDRESS'   => [
      'def'    => 'validate-ip-address',
      'name'   => 'Validate IP-address',
      'module' => 'login',
      'action' => 'validateipaddress',
    ],
    'M_EXTERNAL'              => [
      'def'    => 'external',
      'name'   => 'External',
      'module' => 'external',
      'action' => 'main',
    ],
    'M_SHOP_CATEGORY'         => [
      'def'    => 'categorie',
      'name'   => 'Categorie',
      'module' => 'siteshop',
      'action' => 'category',
    ],
    'M_PRODUCTS'              => [
      'def'    => 'product',
      'name'   => 'Product',
      'module' => 'siteshop',
      'action' => 'product',
    ],
    'M_BASKET'                => [
      'def'    => 'winkelmandje',
      'name'   => 'Winkelmandje',
      'module' => 'basket',
      'action' => 'list',
    ],
    'M_SETTINGS'              => [
      'def'    => 'mijn-gegevens-ov',
      'name'   => 'Mijn gegevens',
      'module' => 'user',
      'action' => 'firstchild',
    ],
    'M_SETTINGS_PERS'         => [
      'def'    => 'mijn-gegevens',
      'name'   => 'Mijn gegevens',
      'module' => 'basket',
      'action' => 'newuser',
    ],
    'M_SETTINGS_OTHER'        => [
      'def'    => 'overige-gegevens',
      'name'   => 'Overige gegevens',
      'module' => 'user',
      'action' => 'infoOther',
    ],
    'M_SETTINGS_ADDRESS'      => [
      'def'    => 'mijn-afleveradressen',
      'name'   => 'Afleveradressen',
      'module' => 'user',
      'action' => 'address_site',
    ],
    'M_SETTINGS_ORDERHISTORY' => [
      'def'    => 'mijn-bestelgeschiedenis',
      'name'   => 'Bestelgeschiedenis',
      'module' => 'user',
      'action' => 'orderhistory',
    ],
    'M_ERROR'                 => [
      'def'    => 'error',
      'name'   => 'Error',
      'module' => 'main',
      'action' => 'error',
    ],
    'M_ERROR_RIGHTS'          => [
      'def'    => 'error-rechten',
      'name'   => 'Error-rechten',
      'module' => 'main',
      'action' => 'errorrights',
    ],
    'M_TAGS'                  => [
      'def'    => 'tags',
      'name'   => 'Tags',
      'module' => 'tag',
      'action' => 'list',
    ],
    'M_BRANDS'                => [
      'def'    => 'merk',
      'name'   => 'Merk',
      'module' => 'siteshop',
      'action' => 'brand',
    ],
    'M_FEED_BESLIST'          => [
      'def'    => 'beslistfeed',
      'name'   => 'Beslistfeed',
      'module' => 'feeds',
      'action' => 'beslist',
    ],
    'M_FEED_SITEMAP'          => [
      'def'    => 'googlesitemap',
      'name'   => 'Googlesitemap',
      'module' => 'feeds',
      'action' => 'sitemap',
    ],
    'M_FEED_GOOGLESHOPPING'   => [
      'def'    => 'googleshopping',
      'name'   => 'Googleshopping',
      'module' => 'feeds',
      'action' => 'googleshopping',
    ],
    'M_FEED_SHOPINSHOP'       => [
      'def'    => 'shopinshop',
      'name'   => 'Shopinshop',
      'module' => 'feeds',
      'action' => 'shopinshop',
    ],
    'M_API_GETSHIPPINGCOSTS'  => [
      'def'    => 'getshippingcosts',
      'name'   => 'Getshippingcosts',
      'module' => 'api',
      'action' => 'getshippingcosts',
    ],
    'M_WISHLIST'              => [
      'def'    => 'verlanglijstjes',
      'name'   => 'Verlanglijstjes',
      'module' => 'wishlist',
      'action' => 'list',
    ],
    'M_SITEMAP'               => [
      'def'    => 'sitemap',
      'name'   => 'Sitemap',
      'module' => 'site',
      'action' => 'sitemap',
    ],
    'M_SEARCH'                => [
      'def'    => 'zoeken',
      'name'   => 'Zoeken',
      'module' => 'site',
      'action' => 'search',
    ],
    'M_SPECIAL_GENERATEPDF'   => [
      'def'    => 'genereerpdf',
      'name'   => 'Genereer PDF',
      'module' => 'pdf',
      'action' => 'generatepdfext',
    ],
  ];