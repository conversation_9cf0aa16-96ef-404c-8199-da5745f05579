<script type="text/javascript">
  $(document).ready(function(){
	    $("input[type='radio']").on("click", function() {
				$("#refresh").trigger("click");
		  });
  });
</script>
<div id="pay1" class="container_payment">
  <div class="container_payment_head">
  	<h1><?php echo $wizard_name ?> - <?php echo strtolower(__('STEP_SHIPPING_COSTS')); ?></h1>
  </div>
  <div class="container_payment_content">
    <table class="steps" border="0">
      <tr>
        <td class="step_on"><?php echo __('STEP_SHIPPING_COSTS')?></td>
        <?php if(count($paymethods)>1): ?>
          <td class="step_off">Betaalmethode</td>
        <?php endif; ?>
        <td class="step_off">Bevestigen</td>
        <td class="step_off">Gereed</td>
      </tr>
    </table>
    <br/>
    <?php writeErrors($errors); ?>
    <form method="post">
    <input type="hidden" value="1" name="basket_helper" />
    <b><?php echo $_SESSION['userObject']->getNaam() ?>,</b>
    <br/><br/>
    
    <?php if(isset($sellmessage) && $sellmessage!=""): ?>
      <div id="sellmessage"><?php echo $sellmessage ?></div>
    <?php endif; ?>
    <?php if(count($shippingmethods)>0): ?>
    	Selecteer uw verzendmethode:<br/>
      <?php foreach($shippingmethods as $methode=>$sh_price): ?>
      <label><input type="radio" value="<?php echo $methode ?>" name="shipping_method" id="radio_<?php echo $methode; ?>" <?php if($order['shipping']['shipping_method']==$methode) echo 'checked="checked"'; ?>/> <?php echo __('SHIPPING_METHODDESC_'.$methode) ?></label><br/>
      <?php endforeach; ?>
      <br/>
    <?php endif; ?>
    Selecteer uw afleveradres:
    <div id="shippingadres">
      <?php if(count($shippingadresses)==0): ?>
      <br/>
       U heeft nog geen afleveradressen. Voer een nieuw afleveradres in door op onderstaande knop te klikken.
      <?php else: ?>
      <table class="standard">
      	<tr>
      		<td  style="vertical-align: middle;padding-right: 10px;" >Selecteer: </td>
      		<?php foreach($shippingadresses as $ship): ?>
      			<td><input type="radio" value="<?php echo $ship->id; ?>" name="shipping_address_id" id="radio_<?php echo $ship->id; ?>" <?php if($order['shipping']['shipping_address']->id==$ship->id) echo 'checked="checked"'; ?>/></td>
      		<?php endforeach; ?>
      	</tr>
      	<tr>
      		<td>Adres: </td>
      		<?php foreach($shippingadresses as $ship): ?>
      			<td style="padding-right: 10px;"><label for="radio_<?php echo $ship->id; ?>">
      				<?php if(Config::isTrue("BASKET_PICKUP_ENABLED") && $ship->id==0):?>
      					<b><?php echo $ship->contactname; ?><br/>
      					Geen verzendkosten<br/></b>
      					Afhaaldagen op afspraak: <br/>
      					maandag t/m zaterdag 	8.00 - 18.00 uur
      				<?php else: ?>
      		      <?php if($ship->contactname!=""): ?>T.a.v. <?php echo $ship->contactname; endif; ?>
      				<?php endif; ?>
      				<br/>
      			  <?php echo $ship->getAddressDetails(); ?></label>
      			</td>
      		<?php endforeach; ?>
      	</tr>
      </table>
      <?php endif; ?>
    </div>
    <div class="clear"></div>
    <br/><a href="<?php echo PageMap::getUrl('M_SETTINGS_ADDRESS') ?>" class="gsd-btn">Wijzig/nieuw afleveradres</a>
    <div class="clear"></div>
    <div id="shippingcosts">	
    	<table>
    	  <?php if(count($shippingmethods)>0) : ?>
      		<tr>
        		<td style="width: 140px;font-weight: bold;">Verzendkosten: </td>
        		<td style="font-weight: bold;"> € 
        		<?php if(Config::get('PRODUCTS_SHOW_PRICES_INC_VAT',true)): ?> 
        		  <?php echo getLocalePrice($order['shipping']['costs']*Invoice::determineVatNewCalcM()) ?> incl. BTW
            <?php else: ?>
          		<?php echo getLocalePrice($order['shipping']['costs']) ?> excl. BTW
        		<?php endif; ?></td>
        	</tr>
      	<?php endif; ?>
    	</table>
    </div>
    <br/>
    
    <input type="button" value="Naar winkelmandje" name="prev" onclick="location.href='<?php echo PageMap::getUrl('M_BASKET') ?>';" />
    <input type="button" value="Verder winkelen" name="prev" onclick="location.href='/';" />
    <input type="submit" value="Volgende stap" name="next" id="next" style="float: right;" />
    <input type="submit" value="Refresh" name="refresh" id="refresh" style="display:none;" />
    <div class="clear"></div>
    </form>
	</div>
</div>    
