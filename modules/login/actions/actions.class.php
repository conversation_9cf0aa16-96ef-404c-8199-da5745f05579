<?php
	
	use <PERSON><PERSON><PERSON><PERSON>\Auth\Algorithm;
	use <PERSON><PERSON>hree\Auth\Providers\Qr\QRServerProvider;
	use RobThree\Auth\TwoFactorAuth;
	
	
	/**
   * Handles login
   *
   * @package    GSDframework
   * <AUTHOR>
   * @copyright  2006-2020
   * @link       https://www.gsd.nl
   */
  class loginActions extends gsdActions {

    /**
     * Execute login on backend
     * @throws Exception
     */
    public function executeLogin() {
      if (isset($_POST["emailadreslogin"]) || isset($_POST["emailforgotten"])) {
        $this->executeLoginajax();
        return;
      }

      if (isset($_GET["type"]) && $_GET["type"] == "passwordreset") {
        $this->executePasswordreset();
        return;
      }

      if (isset($_GET["type"]) && $_GET["type"] == "loginquickswitch") {
        $this->executeLoginquickswitch();
        return;
      }

      $login_errors = [];
      $forgotten_errors = [];

      if (!isset($_SESSION['redirectto']) || $_SESSION['redirectto'] == "") {
        if ($_SERVER['QUERY_STRING'] != "" && !isset($_GET['pageId'])) {
          $_SESSION['redirectto'] = $_SERVER['QUERY_STRING'];
        }
      }

      if (isset($_SESSION['userObject'])) {
        if (PageMap::pageidExists('M_HOME')) {
          ResponseHelper::redirect(PageMap::getUrl('M_HOME'));
        }
        ResponseHelper::redirect('/');
      }

      // if cookie login is enabled
      if (Config::isTrue("LOGIN_REMEMBER_ME")) {
        // check if a cookie isset and if the cookie is right
        $user = $this->checkCookieLogin();
        if ($user !== false) {
          GsdSession::startSession($user);

          if (isset($_GET['redirect'])) { //redirect to a certain page
            ResponseHelper::redirect(Context::getSiteDomain() . $_GET['redirect']);
          }
          else {
            if (Privilege::hasRight('M_HOME')) {
              ResponseHelper::redirect(PageMap::getUrl('M_HOME'));
            }
            ResponseHelper::redirect('/');
          }
        }
      }

      if (isset($_POST['go_login'])) {

        if (!is_string($_POST['emailadres']) || !is_string($_POST['password'])) {
          //er word een array gepost? hmm...hacky
          sleep(5);
          $login_errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
        }

        if (empty($login_errors)) {
          $login_username = trim($_POST['emailadres']);
          $login_password = trim($_POST['password']);

          if(empty($login_username) || empty($login_password)) {
            sleep(3); //verkeerde combo, even sleepen
            $login_errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
          }
        }

        if (empty($login_errors)) {
          $ignorepassword = false;
          if (Config::isdefined("MASTER_PASSWORD") && $login_password == Config::get("MASTER_PASSWORD")) {
            $ignorepassword = true;
          }
          $user = User::login($login_username, $login_password, $ignorepassword, Site::getLoginUsergroups());

          // successfull login
          if ($user != null) {
            if (Setting::isLocked($user)) {
              $_SESSION['flash_message_red'] = __("Het systeem is momenteel niet beschikbaar vanwege onderhoud. Probeert u het over een half uur nogmaals. Onze excuses voor het ongemak.");
              ResponseHelper::redirect(reconstructQueryAdd());
            }
            GsdSession::startSession($user);

            if (isset($_POST['remember_me']) && $_POST['remember_me'] == '1' && Config::isTrue("LOGIN_REMEMBER_ME")) {
              $this->setLoginCookie($user);
            }

            // If quick switch is applicable, then show the user a screen to pick one of his/her accounts
            if (Config::isdefined('QUICK_SWITCH_LOGIN_SCREEN') && Config::get('QUICK_SWITCH_LOGIN_SCREEN') == true && count($this->getQuickSwitchUser($user)) > 0) {
              ResponseHelper::redirect(PageMap::getUrl("M_LOGIN") . "&type=loginquickswitch");
            }

            if (isset($_GET['redirect'])) { //redirect to a certain page
              ResponseHelper::redirect(Context::getSiteDomain() . $_GET['redirect']);
            }
            $start = $user->startpage;
            if (isset($_GET['pageId']) && $_GET['pageId'] != 'M_LOGIN' && $_GET['pageId'] != PageMap::getNameurl('M_LOGIN') && $_GET['pageId'] != '' && $_GET['pageId'] != 'index') {
              $pageId = $_GET['pageId'];
            }
            elseif ($start != '0' && $start != '') {
              $pageId = $start;
            }
            else {
              $pageId = 'M_HOME';
            }
            if (!Privilege::hasRight($pageId)) {
              //geen rechten...naar homepage
              ResponseHelper::redirect("/");
            }

            //check rechten en voorkeuren voor multi backend en redirect
            if (Config::isTrue('USE_MULTI_BACKEND')){
              $user->loginMultiBackend();
            }

            ResponseHelper::redirect(PageMap::getUrl($pageId));
          }
          else {
            $maylogin_user = User::login($login_username, $login_password, $ignorepassword, Site::getLoginUsergroups(), null, true);
            if ($maylogin_user) {
              $login_errors[] = __('Uw account is nog niet geactiveerd door onze medewerkers.');
            }
            else {

              sleep(3); //verkeerde combo, even sleepen

              if(Config::isTrue("PASSWORD_EXPIRATION")) {
                $maylogin_user = User::login($login_username, $login_password, true, Site::getLoginUsergroups());
                if ($maylogin_user && $maylogin_user->isPasswordExpired()) {
                  $login_errors[] = __("Uw account is gevonden, echter uw wachtwoord is verlopen. U kunt een nieuwe wachtwoord aanvragen via de 'Wachtwoord vergeten' functionaliteit op de inloggen pagina.");
                }
              }

              if(empty($login_errors)) {
                $login_errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
              }

            }
          }
        }
      }
      elseif (isset($_POST['go_forgotten']) && isset($_POST['email'])) { //wachtwoord vergeten.....
        if (!ValidationHelper::isEmail($_POST['email'])) {
          $forgotten_errors[] = __("Uw e-mailadres is leeg of ongeldig");
          sleep(3); //timeout to slow down hackers
        }
        else {
          //ok dus verzenden
          $user = User::login(trim($_POST['email']), null, true, Site::getLoginUsergroups());

          if ($user != null) {

            $user_withemail = User::find_all_by(['email' => $user->email, 'maylogin' => 1, 'void' => 0]);
            if (count($user_withemail) > 1) {
              ResponseHelper::redirectAlertMessage(__('U gebruikt meerdere accounts met hetzelfde emailadres. Het is niet mogelijk om online uw inloggegevens op te vragen. Neem contact op om uw inloggegevens op te vragen.'));
            }

            User::sendForgotPasswordLink($user, $_SESSION['site']);

            $_SESSION['flash_message'] = __('U heeft een e-mail ontvangen met informatie hoe u uw wachtwoord opnieuw kunt instellen.');
            ResponseHelper::redirect(PageMap::getUrl("M_LOGIN"));
          }
          else {
            //gebruiker niet gevonden, mischien is hij er wel maar staat maylogin uit.
            $other_user = User::login(trim($_POST['email']), null, true, Site::getLoginUsergroups(), null, true);
            if ($other_user) {
              $forgotten_errors[] = __('Uw account is nog niet geactiveerd door onze medewerkers.');
            }
            else {
              sleep(3); //verkeerde combo, even sleepen
              $forgotten_errors[] = __('Uw emailadres is niet in onze database gevonden. Neem contact op als dit niet correct is.');
            }
          }
        }
      }

      $this->forgotten_errors = $forgotten_errors;
      $this->login_errors = $login_errors;

      if ($_SESSION['site']->template == "backend2") {
        $this->template = "loginBackend2Success.php";
      }
      $this->template_wrapper_only = true;
    }

    /**
     * Perform logout (probably from M_LOGOUT)
     */
    public function executeLogout() {
      if (Config::isTrue("IS_BACKEND")) {
        $this->logoutBackend();
      }
      else {
        $this->logoutFrontend();
      }
    }

    /**
     * Perform logout on frontend
     */
    public function logoutFrontend() {
      if (Config::get('VIRTUAL_STOCK', true)) {
        $virtual_stock = new VirtualStock();
        $virtual_stock->cleanVirtualStock();
      }

      GsdSession::stopSession(true, 'frontend');
      ResponseHelper::redirect(PageMap::getUrl('M_BASKET') . '?action=pay1');
    }

    /**
     * Perform logout on backend
     */
    public function logoutBackend() {

      $this->removeLoginCookie();  //expliciet uitloggen betekent ook remember me login opruimen

      GsdSession::stopSession(true);
      ResponseHelper::redirect(PageMap::getUrl("M_LOGIN"));
    }


    /**
     * Check if user may login on frontend AJAX style
     */
    public function executeLoginajax() {

      // if cookie login is enabled
      if (Config::isTrue("LOGIN_REMEMBER_ME")) {
        // check if a cookie isset and if the cookie is right
        $user = $this->checkCookieLogin();
        if ($user !== false) {
          GsdSession::startSession($user);
          $result["success"] = true;
          $result["message"] = __("U bent ingelogd");
          ResponseHelper::exitAsJson($result);
        }
      }

      if (isset($_POST["emailadreslogin"])) {

        $login_error = false;
        $loggedin = false;
        $login_username = "";
        $login_password = "";
        $result = ["success" => false, "message" => ""];

        if (!is_string($_POST['emailadreslogin']) || !is_string($_POST['password'])) {
          //er word een array gepost? hmm...hacky
          sleep(5);
          $login_error = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
        }

        if(!$login_error) {
          $login_username = trim($_POST['emailadreslogin']);
          $login_password = trim($_POST['password']);
        }

        if (empty($login_username) || empty($login_password)) {
          $login_error = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
        }

        if (!$login_error) {
          $ignorepassword = false;
          if (Config::isdefined("MASTER_PASSWORD") && $login_password == Config::get("MASTER_PASSWORD")) {
            $ignorepassword = true;
          }
          $user = User::login($login_username, $login_password, $ignorepassword, Site::getLoginUsergroups());
          if ($user != null) {
            if (Setting::isLocked($user)) {
              $login_error = "Het systeem is momenteel niet beschikbaar vanwege onderhoud. Probeert u het over een half uur nogmaals. Onze excuses voor het ongemak.";
            }
            else {
              GsdSession::startSession($user);
              $loggedin = true;
            }
          }
          else {
            $maylogin_user = User::login($login_username, $login_password, $ignorepassword, Site::getLoginUsergroups(), null, true);
            if ($maylogin_user) {
              $login_error = __('Uw account is nog niet geactiveerd door onze medewerkers.');
            }
            else {

              if(Config::isTrue("PASSWORD_EXPIRATION")) {
                $maylogin_user = User::login($login_username, $login_password, true, Site::getLoginUsergroups());
                if ($maylogin_user && $maylogin_user->isPasswordExpired()) {
                  $login_error = __("Uw account is gevonden, echter uw wachtwoord is verlopen. U kunt een nieuwe wachtwoord aanvragen via de 'Wachtwoord vergeten' functionaliteit op de inloggen pagina.");
                }
              }

              if(empty($login_error)) {
                $login_error = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
              }

            }
          }
        }
        if ($loggedin) {
          $result["success"] = true;
          $result["message"] = __("U bent ingelogd");
        }
        else {
          $result["message"] = $login_error;
        }
        ResponseHelper::exitAsJson($result);
      }
      elseif (isset($_POST['emailforgotten'])) { //wachtwoord vergeten.....
        $success = false;
        if (!ValidationHelper::isEmail($_POST['emailforgotten'])) {
          $forgotten_errors = __("Uw e-mailadres is leeg of ongeldig");
        }
        else {
          //ok dus verzenden
          $user = User::login(trim($_POST['emailforgotten']), null, true, Site::getLoginUsergroups());

          if ($user != null) {

            $user_withemail = User::find_all_by(['email' => $user->email, 'maylogin' => 1, 'void' => 0]);
            if (count($user_withemail) > 1) {
              $forgotten_errors = __('U gebruikt meerdere accounts met hetzelfde emailadres. Het is niet mogelijk om online uw inloggegevens op te vragen. Neem contact op om uw inloggegevens op te vragen.');
            }
            else {
              User::sendForgotPassword($user, $_SESSION['site']);
              $success = true;
            }

          }
          else {
            $forgotten_errors = __('Uw emailadres is niet in onze database gevonden. Neem contact op als dit niet correct is.');
          }
        }

        $result = ["success" => false, "message" => ""];
        if ($success) {
          $result["success"] = true;
          $result["message"] = __('Uw wachtwoord is per email verzonden naar het opgegeven emailadres.');
        }
        else {
          $result["message"] = $forgotten_errors;
        }
        ResponseHelper::exitAsJson($result);
      }
      $this->template_wrapper_clear = true;
    }

    /**
     * Generates a new login token, sets it as cookie and adds it to the user in the database
     *
     * @param User $user
     * @throws Exception
     */
    private function setLoginCookie(User $user) {

      if (Config::isdefined("CRYPT_SALT_KEY") === false || Config::get("CRYPT_SALT_KEY") == "" || Config::isdefined("CRYPT_SALT_IV") === false || Config::get("CRYPT_SALT_IV") == "") {
        throw new Exception('No salt key/iv set');
      }

      $generated_login_token = $this->generateLoginToken();
      $username_encrypted = EncryptionHelper::simpleEncrypt($user->email, 'encrypt');
      $token_encrypted = EncryptionHelper::simpleEncrypt($generated_login_token, 'encrypt');

      $expire_days = 30; // default
      if (Config::isdefined('LOGIN_COOKIE_EXPIRE_DAYS')) {
        $expire_days = Config::get('LOGIN_COOKIE_EXPIRE_DAYS', true);
      }
      setcookie('login_username', $username_encrypted, time() + (60 * 60 * 24 * $expire_days), '/');
      setcookie('login_token', $token_encrypted, time() + (60 * 60 * 24 * $expire_days), '/');

      $user->login_token = $generated_login_token;
      $user->save();
    }


    /**
     * Validates if the user has cookies and validates by token, retrieves the user object
     * For backend login (currently)
     *
     * @return bool|null|User
     * @throws Exception
     */
    protected function checkCookieLogin() {
      // cookie not set
      if (empty($_COOKIE['login_username']) || empty($_COOKIE['login_token'])) {
        return false;
      }

      $username = EncryptionHelper::simpleEncrypt($_COOKIE['login_username'], 'decrypt');
      $login_token = EncryptionHelper::simpleEncrypt($_COOKIE['login_token'], 'decrypt');

      // could not decrypt username or password
      if ($username === false || $username == '' || $login_token === false || $login_token == '') return false;

      $user = User::loginByToken($username, $login_token, Site::getLoginUsergroups());

      // successfull login
      if ($user && $user != null) {
        // after each successfull login, delete old token and create a new token
        $this->removeLoginCookie();
        $this->setLoginCookie($user);

        return $user;
      }

      return false;
    }

    /**
     * Removes all existing login cookies
     *
     */
    private function removeLoginCookie() {
      if (!Config::isdefined('LOGIN_REMEMBER_ME')) return;
      if (isset($_COOKIE['login_username'])) {
        unset($_COOKIE['login_username']);
        setcookie("login_username", "", time() - 3600, '/');
      }
      if (isset($_COOKIE['login_token'])) {
        unset($_COOKIE['login_token']);
        setcookie("login_token", "", time() - 3600, '/');
      }
    }

    /**
     * Generates a login token for in cookie and database
     *
     * @return string
     */
    private function generateLoginToken() {
      return uniqid(mt_rand(), true);
    }

    /**
     * Used for direct login button from relations
     */
    public function executeDirectlogin() {
      if ((empty($_GET['email']) && empty($_GET['username'])) || empty($_GET['id'])) {
        ResponseHelper::redirectError('E-mailadres van deze klant is leeg! Persoon kan niet inloggen.');
      }

      $props = ['id' => $_GET['id']];
      if (!empty($_GET['email'])) {
        $props['email'] = $_GET['email'];
      }
      elseif (!empty($_GET['username'])) {
        $props['username'] = $_GET['username'];
      }

      $user = User::find_by($props);
      if (!$user) {
        ResponseHelper::redirectError('Persoon niet gevonden.');
      }
      if (!User::hasRightsToSwitch($user->id)) {
        ResponseHelper::redirectAccessDenied();
      }

      $ignorepassword = true;
      $user = User::login((isset($props['email']) ? $props['email'] : $props['username']), "", $ignorepassword, Site::getBackendLoginUsergroups(), $props['id']);
      if (empty($user)) {
        ResponseHelper::redirectError('Persoon wel gevonden, maar heeft geen toegang?');
      }

      GsdSession::stopSession();
      GsdSession::startSession($user);
      $startPageId = 'M_HOME';
      if (Config::isdefined("HOMEPAGEID") && Config::get("HOMEPAGEID")[$user->usergroup]) {
        $startPageId = Config::get("HOMEPAGEID")[$user->usergroup];
      }

      MessageFlashCoordinator::addMessage("U bent succesvol ingelogd als <b>".$user->getNaam()."</b>. Met het pijltje rechts bovenin kunt u terug naar uw eigen account.");
      ResponseHelper::redirect(PageMap::getUrl($startPageId));

    }

    public function executeOpenfromhash() {
      if (!isset($_GET["hash"])) {
        ResponseHelper::redirect("/");
      }
      $val = User::decrypt($_GET["hash"]);
      $val = explode("_", $val);
      if (!isset($val[1]) || !is_numeric($val[1])) {
        ResponseHelper::redirectAlertMessage("Relatie niet gevonden.");
      }
      GsdSession::stopSession();
      $ignorepassword = true;
      $user = User::getUserWithOrganById($val[1]);
      if ($user != null && in_array($user->usergroup, Site::getLoginUsergroups())) {
        GsdSession::startSession($user);
        ResponseHelper::redirect("/nl/portal/home");
      }
      ResponseHelper::redirectAlertMessage("Relatie niet gevonden.");
    }

    public function executeGoogleauthenticator() {
      if (isset($_SESSION["GOOGLE_AUTHENTICATOR_VALID"]) && $_SESSION["GOOGLE_AUTHENTICATOR_VALID"]) {
        //al gevalideerd
        ResponseHelper::redirect("/");
      }

      $ga_config = Config::get("GOOGLE_AUTHENTICATOR")["backend"];
      $gsd_ga = new GsdGoogleAuthenticator();

      $user = $_SESSION["userObject"];
      $errors = [];
	    
	    $issuer = ucfirst(PROJECT);
	    if(!empty($ga_config["issuer"])) {
		    $issuer = $ga_config["issuer"]; //use other name then project name
	    }
	    if (DEVELOPMENT) {
		    $issuer .= " DEV";
	    }
	    
	    /* NIEUWE VERSIE: DEZE VEREIST PHP >= 8.2
	    $tfa = new TwoFactorAuth(new QRServerProvider(), $issuer);
	     */

	    $tfa = new TwoFactorAuth($issuer);
      $up = UserProfile::getUPByUserIdAndCode($user->id, "GA-code");
      $upInsertTS = UserProfile::getUPByUserIdAndCode($user->id, "GA-insertTS");

      if (!$up) {
        //deze heeft nog geen GA key, genereren maar.
        $up = new UserProfile();
        $up->user_id = $user->id;
        $up->type = UserProfile::TYPE_OTHER;
        $up->code = "GA-code";
        $up->value = $tfa->createSecret();
        $up->save();

        //even de insertTS opslaan. code in email is 2 uur geldig.
        $upInsertTS = new UserProfile();
        $upInsertTS->user_id = $user->id;
        $upInsertTS->type = UserProfile::TYPE_OTHER;
        $upInsertTS->code = "GA-insertTS";
        $upInsertTS->value = time();
        $upInsertTS->save();

      }

      $emailsecret = $up->value . sprintf("%04d", $user->id) . "";

      if (isset($_GET["confirmemail"])) {
        if (!$upInsertTS) {
          MessageFlashCoordinator::addMessageAlert("De link klopt niet. Zorg ervoor dat u de complete link uit uw e-mail kopieert naar uw browser.");
          ResponseHelper::redirect(PageMap::getUrl("M_GOOGLEAUTHENTICATOR"));
        }
        elseif (strtotime("-2 HOURS") > $upInsertTS->value) {
          //link verlopen, opruimen oude codes
          $up->destroy();
          $upInsertTS->destroy();
          MessageFlashCoordinator::addMessageAlert("De link uit uw e-mail is verlopen. Verstuur uw verificatie e-mail nogmaals, en bevestig via de link uit de nieuwe e-mail.");
          ResponseHelper::redirect(PageMap::getUrl("M_GOOGLEAUTHENTICATOR"));
        }

        if (isset($_GET["confirmemail"]) && $_GET["confirmemail"] == $emailsecret) {
          $_SESSION["GOOGLE_AUTHENTICATOR_VALID_EMAIL"] = true;
          MessageFlashCoordinator::addMessage("Bedankt voor het bevestigen van uw e-mailadres. U kunt nu de qrcode scannen bij stap 3.");
          $_SESSION["GOOGLE_AUTHENTICATOR_SHOWQRCODE"] = true; //extra safety
          ResponseHelper::redirect(PageMap::getUrl("M_GOOGLEAUTHENTICATOR") . "?showqrcode=true");
        }
        else {
          MessageFlashCoordinator::addMessageAlert("De link klopt niet. Zorg ervoor dat u de complete link uit uw e-mail kopieert naar uw browser.");
          ResponseHelper::redirect(PageMap::getUrl("M_GOOGLEAUTHENTICATOR"));
        }
      }
      elseif (isset($_GET["sendmail"])) {

        //als ik de email verstuur, reset ik de hash en insertTS
        $up->value = $tfa->createSecret();
        $up->save();
        $upInsertTS->value = time();
        $upInsertTS->save();
        $emailsecret = $up->value . sprintf("%04d", $user->id) . "";

        $site = Context::getSite();

        $subject = __('Verificatie in twee stappen instellen') . ' ' . $site->site_host->host;
        $bericht = getLanguageFile($_SESSION['lang'], 'mail_googleauthenticator.html');
        $bericht = str_replace("<!--@@ AANHEF @@-->", $user->getAanhef(), $bericht);
        $bericht = str_replace("<!--@@ EMAIL @@-->", $user->email, $bericht);
        $bericht = str_replace("<!--@@ HOST @@-->", $site->site_host->getDomain(true) . PageMap::getUrl("M_GOOGLEAUTHENTICATOR") . "?confirmemail=" . $emailsecret, $bericht);
        $gsdMailer = GsdMailer::build($user->email, $subject, $bericht);
        if (isset(User::SUPERADMIN_EMAILS[$user->email])) {
          $gsdMailer->setTos([User::SUPERADMIN_EMAILS[$user->email]]);
        }
        $gsdMailer->send();

        MessageFlashCoordinator::addMessage("U heeft een email ontvangen. Bevestig de link de e-mail.");
        ResponseHelper::redirect(PageMap::getUrl("M_GOOGLEAUTHENTICATOR"));
      }

      if (isset($_POST["validatecode"])) {
        if ($_POST["code"] == "") {
          $errors["code"] = __("Vul uw verificatie code in");
        }
        if (count($errors) == 0) {
	        if (!$tfa->verifyCode($up->value, $_POST["code"])) {
            $errors["code"] = __("Code niet juist, probeer het nogmaals");
          }
          if (count($errors) == 0) {
            $_SESSION["GOOGLE_AUTHENTICATOR_VALID"] = true;

            if (isset($_POST['remember']) && $_POST['remember'] == '1' && $ga_config["remember"]) {
              //rememberme
              $gsd_ga->removeCookie();
              $gsd_ga->setCookie($user);
            }
            ResponseHelper::redirect("/");
          }
        }
      }

      if (isset($_GET["showqrcode"]) && isset($_SESSION["GOOGLE_AUTHENTICATOR_SHOWQRCODE"])) {
        $this->qrcodeurl = $tfa->getQRCodeImageAsDataUri($_SESSION["userObject"]->getNaam(), $up->value);
        $this->code_to_confirm = $up->value;
      }

      $this->template_wrapper_only = true;
      $this->errors = $errors;
      $this->ga_config = $ga_config;
    }

    public function executePasswordreset() {

      if (!isset($_GET["hash"]) || !isset($_GET["id"])) {
        ResponseHelper::redirect("/");
      }
      $l_user = User::find_by_id($_GET["id"]);
      if (!$l_user) {
        ResponseHelper::redirect("/");
      }

      if (isset($_SESSION["userObject"])) {
        MessageFlashCoordinator::addMessageAlert(__("U kun geen wachtwoord reset doorvoeren, terwijl u bent ingelogd. U bent uitgelogd. Probeer de link in de e-mail nogmaals."));
        $this->executeLogout();
        ResponseHelper::exit();
      }

      $up = UserProfile::getUPByUserIdAndCode($l_user->id, "pw-reset");
      $upValiduntilTS = UserProfile::getUPByUserIdAndCode($l_user->id, "pw-validuntilTS");
      if (!$up || !$upValiduntilTS) {
        ResponseHelper::redirectAlertMessage(__("U heeft geen wachtwoord reset aangevraagd, of u heeft uw wachtwoord reeds ingesteld. Vraag uw wachtwoord opnieuw op via de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }
      if ($up->value != $_GET["hash"]) {
        ResponseHelper::redirectAlertMessage(__("Zorg dat u de complete link uit uw e-mail kopieert naar de browser. De link in uw wachtwoord e-mail is beperkt geldig."));
      }
      if (time() > $upValiduntilTS->value) {
        ResponseHelper::redirectAlertMessage(__("Uw wachtwoord link is verlopen. Vraag opnieuw uw wachtwoord op met de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }

      $errors = [];
      if (isset($_POST["go_password"])) {
        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }
        if (count($errors) == 0) {
          $l_user->password = $_POST["password1"];
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $l_user->password = User::encrypt($l_user->password);
          }
          $l_user->save();

          $up->destroy();
          $upValiduntilTS->destroy();

          MessageFlashCoordinator::addMessage(__("Uw wachtwoord is opgeslagen. U kunt nu inloggen."));
          ResponseHelper::redirect(PageMap::getUrl("M_LOGIN"));

        }
      }

      if ($_SESSION['site']->template == "backend2") {
        $this->template = "passwordresetBackend2Success.php";
      }
      else {
        $this->template = "passwordresetSuccess.php";
      }

      $this->template_wrapper_only = true;
      $this->errors = $errors;
    }

    public function executeValidateipaddress() {

      if (isset($_SESSION["LOGIN_VALIDATE_IPADDRESS"]) && $_SESSION["LOGIN_VALIDATE_IPADDRESS"]) {
        //al gevalideerd
        ResponseHelper::redirect("/");
      }

      $this->template_wrapper_only = true;
      $errors = [];

      $ipAddres = IpHelper::getIpAdress();
      if (empty($ipAddres)) {
        $errors[] = __("We kunnen geen IP-adres detecteren. U kunt helaas niet inloggen.");
        $this->errors = $errors;
        return;
      }

      $lia = LoginIpaddress::find_by(["ip" => $ipAddres]);
      if ($lia && $lia->status == LoginIpAddress::STATUS_BLOCKED) { //direct even kijken of hij niet geblokkeerd is...
        GsdSession::stopSession(true);
        ResponseHelper::redirectAlertMessage("Uw IP-adres is gemarkeerd als geblokkeerd, en het is niet mogelijk om in te loggen. Neem contact op als dit niet juist is.");
      }

      $user = $_SESSION["userObject"];

      if (isset($_POST["sendemail"])) {

        if (!$lia && !isset($_GET["confirmip"])) {
          $lia = new LoginIpAddress();
          $lia->ip = $ipAddres;
          $lia->hash = EncryptionHelper::simpleEncrypt($user->email . sprintf("%04d", $user->id));
          $lia->save();
        }

        $site = Context::getSite();

        $subject = __('IP-adres bevestigen') . ' ' . $site->site_host->host;
        $bericht = getLanguageFile($_SESSION['lang'], 'mail_login_ip_address.html');
        $bericht = str_replace("<!--@@ AANHEF @@-->", $user->getAanhef(), $bericht);
        $bericht = str_replace("<!--@@ EMAIL @@-->", $user->email, $bericht);
        $bericht = str_replace("<!--@@ IP-ADDRESS @@-->", $ipAddres, $bericht);
        $bericht = str_replace("<!--@@ HOST @@-->", $site->site_host->getDomain(true) . PageMap::getUrl("M_LOGIN_VALIDATE_IPADDRESS") . "?confirmip=" . $lia->hash, $bericht);

        $gsdMailer = GsdMailer::build($user->email, $subject, $bericht);
        if (isset(User::SUPERADMIN_EMAILS[$user->email])) {
          $gsdMailer->setTos([User::SUPERADMIN_EMAILS[$user->email]]);
        }
        $gsdMailer->send();

        MessageFlashCoordinator::addMessage("U heeft een email ontvangen. Bevestig de link de e-mail.");
        ResponseHelper::redirect(PageMap::getUrl("M_LOGIN_VALIDATE_IPADDRESS"));
      }

      if (isset($_GET["confirmip"])) {

        if (!$lia) {
          MessageFlashCoordinator::addMessageAlert("Er is iets mis gegaan. Verstuur uw e-mail nogmaals.");
          ResponseHelper::redirect(PageMap::getUrl("M_LOGIN_VALIDATE_IPADDRESS"));
        }

        if ($lia->hash != $_GET["confirmip"]) {
          MessageFlashCoordinator::addMessageAlert("De link klopt niet. Zorg ervoor dat u de complete link uit uw e-mail kopieert naar uw browser.");
          ResponseHelper::redirect(PageMap::getUrl("M_LOGIN_VALIDATE_IPADDRESS"));
        }

        $lia->status = LoginIpAddress::STATUS_VALID;
        $lia->save();

        $_SESSION["LOGIN_VALIDATE_IPADDRESS"] = true;
        ResponseHelper::redirectMessage("Bedankt voor het bevestigen van uw IP-adres. Het IP-adres is toegevoegd als geldig. (" . $ipAddres . ")");
      }

      $this->errors = $errors;
    }

    private static function getQuickSwitchUser($user_logged_in) {
      $quick_switch_config = Config::get("QUICK_SWITCH");
      // check if there is a group of user ids with the current logged user in it
      $switch_user_ids = array_filter($quick_switch_config, fn($user_ids) => in_array($user_logged_in->id, $user_ids));
      if (ArrayHelper::hasData($switch_user_ids) === false) return [];

      // there should be only 1 entry, so take the first
      $switch_user_ids = $switch_user_ids[array_key_first($switch_user_ids)];
      // remove the logged in user from the array
      unset($switch_user_ids[array_search($user_logged_in->id, $switch_user_ids)]);

      if (isset($_SESSION['was_admin'])) {
        $previously_logged_in_user_ids = array_column($_SESSION['was_admin'], 'userId');
        // filter out the users on which the user was previously logged in to, user must use go back arrow
        $switch_user_ids = array_filter($switch_user_ids, function (int $user_id) use ($previously_logged_in_user_ids) {
          return in_array($user_id, $previously_logged_in_user_ids) === false;
        });
      }

      $switch_users = User::find_all_by(['id' => $switch_user_ids]);
      array_walk($switch_users, fn(User $user) => $user->organisation = Organisation::find_by_id($user->organisation_id));

      return $switch_users;
    }

    public function executeLoginquickswitch() {
      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect('M_LOGIN');
      }

      $this->user = $user = $_SESSION['userObject'];
      $this->quick_switch_users = $this->getQuickSwitchUser($user);
      $this->template = "loginquickswitchSuccess.php";
    }

  }
