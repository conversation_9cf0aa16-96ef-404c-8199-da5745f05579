<?php
class BaseOrganisationAddress extends AppModel
{
  const DB_NAME = '';
  const TABLE_NAME = 'organisation_address';
  const OM_CLASS_NAME = 'OrganisationAddress';
  const columns = ['id', 'organisation_id', 'type', 'is_default', 'is_default_delivery', 'organisation_name', 'contactname', 'address', 'number', 'extension', 'zip', 'city', 'country', 'email', 'phone_nr', 'lat', 'lng', 'void', 'insertTS', 'updateTS', 'insertUser', 'updateUser'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'organisation_id'             => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'type'                        => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'is_default'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'is_default_delivery'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'organisation_name'           => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'contactname'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'address'                     => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'number'                      => ['type' => 'varchar', 'length' => '45', 'null' => true],
    'extension'                   => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'zip'                         => ['type' => 'varchar', 'length' => '12', 'null' => true],
    'city'                        => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'country'                     => ['type' => 'char', 'length' => '2', 'null' => true],
    'email'                       => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'phone_nr'                    => ['type' => 'varchar', 'length' => '25', 'null' => true],
    'lat'                         => ['type' => 'float', 'length' => '10,7', 'null' => true],
    'lng'                         => ['type' => 'float', 'length' => '10,7', 'null' => true],
    'void'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'insertUser'                  => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'updateUser'                  => ['type' => 'varchar', 'length' => '20', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $organisation_id, $type, $is_default, $is_default_delivery, $organisation_name, $contactname, $address, $number, $extension, $zip, $city, $country, $email, $phone_nr, $lat, $lng, $void, $insertTS, $updateTS, $insertUser, $updateUser;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->type = 'delivery';
    $this->is_default = 0;
    $this->is_default_delivery = 0;
    $this->country = 'NL';
    $this->void = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrganisationAddress[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrganisationAddress[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return OrganisationAddress[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrganisationAddress
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return OrganisationAddress
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}