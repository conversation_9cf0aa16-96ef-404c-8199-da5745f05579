<?php
class BaseInvoice extends AppModel
{
  const DB_NAME = '';
  const TABLE_NAME = 'invoice';
  const OM_CLASS_NAME = 'Invoice';
  const columns = ['id', 'order_id', 'user_id', 'organisation_id', 'from_user_id', 'from_organisation_id', 'type', 'variant', 'is_repeat_invoice', 'invoice_nr', 'betreft', 'status', 'total_excl_prods', 'paymentdiscount', 'paymentmethod', 'paymentmethod_id', 'paymentmethod_desc', 'shipping', 'shipping_method', 'shipping_desc', 'shipping_overwrite', 'handle_costs', 'credits', 'total_excl', 'vat', 'total', 'deliverydate', 'invoicedate', 'payeddate', 'reminder1date', 'reminder2date', 'reminder3date', 'paymentterm', 'ivoid', 'remark', 'remark_extra', 'remark_pdf', 'remark_internal', 'related_organid', 'flag', 'flag_2', 'flag_3', 'external_id', 'insertTS', 'updateTS'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'order_id'                    => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'user_id'                     => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'organisation_id'             => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'from_user_id'                => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'from_organisation_id'        => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'type'                        => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'variant'                     => ['type' => 'varchar', 'length' => '25', 'null' => true],
    'is_repeat_invoice'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'invoice_nr'                  => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'betreft'                     => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'status'                      => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'total_excl_prods'            => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'paymentdiscount'             => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'paymentmethod'               => ['type' => 'varchar', 'length' => '45', 'null' => true],
    'paymentmethod_id'            => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'paymentmethod_desc'          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'shipping'                    => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'shipping_method'             => ['type' => 'varchar', 'length' => '45', 'null' => true],
    'shipping_desc'               => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'shipping_overwrite'          => ['type' => 'boolean', 'length' => '1', 'null' => true],
    'handle_costs'                => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'credits'                     => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'total_excl'                  => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'vat'                         => ['type' => 'decimal', 'length' => '10,2', 'null' => false],
    'total'                       => ['type' => 'decimal', 'length' => '10,2', 'null' => true],
    'deliverydate'                => ['type' => 'date', 'length' => '', 'null' => true],
    'invoicedate'                 => ['type' => 'date', 'length' => '', 'null' => true],
    'payeddate'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder1date'               => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder2date'               => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder3date'               => ['type' => 'date', 'length' => '', 'null' => true],
    'paymentterm'                 => ['type' => 'smallint', 'length' => '6', 'null' => false],
    'ivoid'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'remark_extra'                => ['type' => 'text', 'length' => '', 'null' => true],
    'remark_pdf'                  => ['type' => 'text', 'length' => '', 'null' => true],
    'remark_internal'             => ['type' => 'text', 'length' => '', 'null' => true],
    'related_organid'             => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'flag'                        => ['type' => 'boolean', 'length' => '1', 'null' => true],
    'flag_2'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'flag_3'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'external_id'                 => ['type' => 'varchar', 'length' => '38', 'null' => true],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
    'updateTS'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $order_id, $user_id, $organisation_id, $from_user_id, $from_organisation_id, $type, $variant, $is_repeat_invoice, $invoice_nr, $betreft, $status, $total_excl_prods, $paymentdiscount, $paymentmethod, $paymentmethod_id, $paymentmethod_desc, $shipping, $shipping_method, $shipping_desc, $shipping_overwrite, $handle_costs, $credits, $total_excl, $vat, $total, $deliverydate, $invoicedate, $payeddate, $reminder1date, $reminder2date, $reminder3date, $paymentterm, $ivoid, $remark, $remark_extra, $remark_pdf, $remark_internal, $related_organid, $flag, $flag_2, $flag_3, $external_id, $insertTS, $updateTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->type = 'invoice';
    $this->is_repeat_invoice = 0;
    $this->status = 'new';
    $this->vat = 0.00;
    $this->paymentterm = 21;
    $this->ivoid = 0;
    $this->flag_2 = 0;
    $this->flag_3 = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoice[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoice[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Invoice[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoice
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return Invoice
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}