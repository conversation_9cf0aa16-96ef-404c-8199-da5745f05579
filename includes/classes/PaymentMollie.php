<?php

  use <PERSON><PERSON>\Api\Exceptions\ApiException;
  use <PERSON><PERSON>\Api\MollieApiClient;

  class PaymentMollie extends Payment {

    public function __construct() {
      $this->key = Payment::PAYMENT_MOLLIE;
      $this->enable_discount = false;

      $mollie_config = Config::get("PAYMENT_MOLLIE");
      $this->title = isset($mollie_config['title']) ? $mollie_config['title'] : 'Mollie: iDeal e.a. ';
      $this->short_title = isset($mollie_config['short_title']) ? $mollie_config['short_title'] : 'Mollie';
      $this->setLogoUrl("/images/paymentlogos/ideal.png");
    }

    /**
     * Check if payment method is available in current config
     * @return bool
     */
    public static function isAvailable(): bool {
      if (Config::isdefined('PAYMENT_MOLLIE')) {
        $mollie = Config::get('PAYMENT_MOLLIE');
        if ($mollie['enabled'] && $mollie['api-key'] != "") {
          return true;
        }
      }
      return false;
    }

    /**
     * @return MollieApiClient
     * @throws ApiException
     */
    public static function getMollie() {
      $mollieconfig = Config::get('PAYMENT_MOLLIE');
      $mollie = new Mollie\Api\MollieApiClient();
      $mollie->setApiKey($mollieconfig['api-key']);
      return $mollie;
    }

    /**
     * @param Orders $order
     * @param Invoice $invoice
     * @param bool|string $method : voorselectie betaalmethode
     * @return void
     */
    public function handlePayment($order, $invoice, $method = false) {
//    $luser = User::getUserWithOrganById($order->user_id);

      // Gebruik Ngrok zodat mollie een localhost url kan aanspreken voor het testen, url voorbeeld: http://c17d8580.ngrok.io/nl/external?action=paymentpostback&payvariant=mollie
      // gebruik dit commando om op te starten en door te verwijzen: "ngrok http -host-header=gsd.nl.gsd.localhost 80"
      // bekijk vervolgens via: http://127.0.0.1:4040/inspect/http

      $values = [];
      try {
        $mollie = PaymentMollie::getMollie();
        $url = Context::getSiteDomain();
        if (DEVELOPMENT) {
          //$url = "http://8fdd9892.ngrok.io";
        }
        $description = PROJECT . " webshop " . $invoice->id;
        if ($order) {
          $description = PROJECT . " webshop " . $order->getOrderNr();
        }
        $values = [
          "amount"      => [
            "currency" => "EUR",
            "value"    => number_format($invoice->total, 2, '.', ''),
          ],
          "description" => $description,
          "redirectUrl" => $url . PageMap::getUrl('M_BASKET') . "?action=payfinished&invoice_id=" . $invoice->id,
          "webhookUrl"  => $url . PageMap::getUrl('M_EXTERNAL') . "?action=paymentpostback&payvariant=mollie",
          "locale"      => strtolower($_SESSION['lang']) . '_' . strtoupper($_SESSION['lang']),
          "metadata"    => [
            "invoice_id" => $invoice->id,
          ],
        ];

        if ($method && $method != "") { //sla betaalmethode scherm over, en ga direct de betrefende betaling
          $values["method"] = $method;
        }

        $payment = $mollie->payments->create($values);

        ResponseHelper::redirect($payment->getCheckoutUrl());

      }
      catch (Exception $e) {
        logToFile('mollie_exception', 'handlepayment: invoiceid=' . $invoice->id . ' Error: ' . $e->getMessage() . "\nPost: " . print_r($values, true));
      }

    }

    /**
     * Handles postback from paymentserver
     * @return void
     * @throws GsdException
     * @throws ApiException
     */
    public static function paymentpostback() {

      if ($_SERVER['REQUEST_METHOD'] != 'POST') {
        logToFile("mollie", 'payment_postback mollie: NO POST');
        return;
      }

      logToFile("mollie", "start Mollie postback " . print_r($_POST, true));

      $mollie = PaymentMollie::getMollie();
      try {
        $payment = $mollie->payments->get($_POST["id"]);
      }
      catch (ApiException $e) {
        if($e->getCode()==404) {
          //if token not found, do not throw error and return: No payment exists with token ...
          logToFile("mollie", 'payment_postback mollie: '.$e->getMessage());
          return;
        }
        throw $e;
      }
      $invoice_id = $payment->metadata->invoice_id;

      if (empty($invoice_id)) {
        logToFile("mollie", 'payment_postback mollie: invoice_id leeg');
        return;
      }
      logToFile("mollie", 'payment_postback mollie: invoice_id=' . $invoice_id);

      $invoice = Invoice::getInvoiceAndProductsById($invoice_id);

      if (!$invoice) {
        logToFile("mollie", 'payment_postback mollie: INVOICE NIET GEVONDEN');
        return;
      }

      if ($invoice->status == Invoice::INVOICE_STATUS_PAYED) {
        logToFile("mollie", 'payment_postback mollie: factuur is al betaald. overgeslagen. Status = ' . $payment->status);
        return;
      }

      if ($invoice->paymentmethod != Payment::PAYMENT_MOLLIE) {
        logToFile("mollie", 'payment_postback mollie: factuur mag niet met mollie worden betaald. Paymentmethod = ' . $invoice->paymentmethod);
        return;
      }

      //Controle uitgevoerd, en alles top. Mooi.

      $invoice->paymentmethod_id = $_POST["id"];
      $invoice->save();

      if ($payment->isPaid()) { //succesvolle betaling!
        $to_user = User::getUserWithOrganById($invoice->user_id);
        Trans::loadMainLanguagefile($to_user->locale);
        $invoice->paid_online();
        logToFile("mollie", 'payment_postback mollie: factuur betaald');
      }
      elseif ($payment->isCanceled()) {
        logToFile("mollie", 'payment_postback mollie: factuur geanulleerd');
      }
      elseif ($payment->isExpired()) {
        logToFile("mollie", 'payment_postback mollie: factuur verlopen');
      }
      else {
        logToFile("mollie", 'payment_postback mollie: factuur afgebroken: status = ' . $payment->status);
      }

      logToFile("mollie", 'payment_postback mollie: done');

    }

    /**
     * @return array
     * @throws GsdException
     */
    public static function getActivePaymentMethods(): array {
      $mollie_config = Config::get("PAYMENT_MOLLIE");
      return $mollie_config["paymentmethods"];
    }


  }
