<?php

  use PhpOffice\PhpSpreadsheet\IOFactory;
  use PhpOffice\PhpSpreadsheet\Spreadsheet;
  use PhpOffice\PhpSpreadsheet\Style\Alignment;
  use PhpOffice\PhpSpreadsheet\Style\Border;
  use PhpOffice\PhpSpreadsheet\Style\Fill;

  AppModel::loadBaseClass('BaseOrganisationAddress');

  class OrganisationAddressModel extends BaseOrganisationAddress {

    const TYPE_DELIVERY = 'delivery';
    const TYPE_WORK = 'work';

    public static $types = [
      self::TYPE_DELIVERY => 'afleveradres',
      self::TYPE_WORK     => 'werkadres',
    ];

    /**
     * @return mixed
     */
    public function getId() {
      return $this->id;
    }

    /**
     * @return mixed
     */
    public function getOrganisationId() {
      return $this->organisation_id;
    }

    /**
     * @param mixed $organisation_id
     */
    public function setOrganisationId($organisation_id) {
      $this->organisation_id = $organisation_id;
    }

    /**
     * @return mixed
     */
    public function getType() {
      return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType($type) {
      $this->type = $type;
    }

    /**
     * @return mixed
     */
    public function getIsDefault() {
      return $this->is_default;
    }

    /**
     * @param mixed $is_default
     */
    public function setIsDefaultDelivery($is_default_delivery) {
      $this->is_default_delivery = $is_default_delivery;
    }

    /**
     * @return mixed
     */
    public function getIsDefaultDelivery() {
      return $this->is_default_delivery;
    }

    /**
     * @return mixed
     */
    public function getOrganisationName() {
      if (strlen($this->organisation_name ?? '') > 0) {
        return $this->organisation_name;
      }
      return $this->getName();
    }

    /**
     * @param mixed $organisation_name
     */
    public function setOrganisationName($organisation_name): void {
      $this->organisation_name = $organisation_name;
    }

    /**
     * @param mixed $is_default
     */
    public function setIsDefault($is_default) {
      $this->is_default = $is_default;
    }

    /**
     * @return mixed
     */
    public function getContactname() {
      return $this->contactname;
    }

    /**
     * @param mixed $contactname
     */
    public function setContactname($contactname) {
      $this->contactname = $contactname;
    }

    /**
     * @return mixed
     */
    public function getAddress() {
      return $this->address;
    }

    /**
     * @param mixed $address
     */
    public function setAddress($address) {
      $this->address = $address;
    }

    /**
     * @return mixed
     */
    public function getNumber() {
      return $this->number;
    }

    /**
     * @param mixed $number
     */
    public function setNumber($number) {
      $this->number = $number;
    }

    /**
     * @return mixed
     */
    public function getZip() {
      return $this->zip;
    }

    /**
     * @param mixed $zip
     */
    public function setZip($zip) {
      $this->zip = $zip;
    }

    /**
     * @return mixed
     */
    public function getCity() {
      return $this->city;
    }

    /**
     * @param mixed $city
     */
    public function setCity($city) {
      $this->city = $city;
    }

    /**
     * @return mixed
     */
    public function getCountry() {
      $countries = Organisation::getCountries();
      return __($countries[$this->country]);
    }

    /**
     * @param mixed $country
     */
    public function setCountry($country) {
      $this->country = $country;
    }

    /**
     * @return mixed
     */
    public function getLat() {
      return $this->lat;
    }

    /**
     * @param mixed $lat
     */
    public function setLat($lat) {
      $this->lat = $lat;
    }

    /**
     * @return mixed
     */
    public function getLng() {
      return $this->lng;
    }

    /**
     * @param mixed $lng
     */
    public function setLng($lng) {
      $this->lng = $lng;
    }

    /**
     * @return mixed
     */
    public function getVoid() {
      return $this->void;
    }

    /**
     * @param mixed $void
     */
    public function setVoid($void) {
      $this->void = $void;
    }

    /**
     * @return mixed
     */
    public function getInsertTS() {
      return $this->insertTS;
    }

    /**
     * @param mixed $insertTS
     */
    public function setInsertTS($insertTS) {
      $this->insertTS = $insertTS;
    }

    /**
     * @return mixed
     */
    public function getUpdateTS() {
      return $this->updateTS;
    }

    /**
     * @param mixed $updateTS
     */
    public function setUpdateTS($updateTS) {
      $this->updateTS = $updateTS;
    }

    /**
     * @return mixed
     */
    public function getInsertUser() {
      return $this->insertUser;
    }

    /**
     * @param mixed $insertUser
     */
    public function setInsertUser($insertUser) {
      $this->insertUser = $insertUser;
    }

    /**
     * @return mixed
     */
    public function getUpdateUser() {
      return $this->updateUser;
    }

    /**
     * @param mixed $updateUser
     */
    public function setUpdateUser($updateUser) {
      $this->updateUser = $updateUser;
    }


    /**
     * Aanmaken default adres van organisatie gegevens.
     * isdefault: dit is hetzelfde adres als aangeven bij de bedrijfslocatie
     * @param Organisation $organ (required)
     * @param string $type (optional) default: delivery
     * @param bool $name (optional) default: false
     * @return OrganisationAddress
     */
    public static function updateDefaultAddressFromOrgan($organ, $type = false, $name = false) {
      if ($type === false) {
        if (Config::isdefined("ORGANISATION_ADDRESS_DEFAULT_TYPE")) {
          $type = Config::get("ORGANISATION_ADDRESS_DEFAULT_TYPE");
        }
        else {
          $type = OrganisationAddress::TYPE_DELIVERY;
        }
      }
      $address = OrganisationAddress::find_by(["organisation_id" => $organ->id, 'is_default' => 1, 'type' => $type, 'void' => 0]);
      if (!$address) {
        $address = new OrganisationAddress();
        $address->is_default = 1;
        $address->is_default_delivery = 1;
        $address->organisation_id = $organ->id;
        $address->type = $type;
      }
      if ($name) {
        $address->contactname = $name;
      }

      $address->organisation_name = $organ->name;
      $address->address = $organ->address;
      $address->number = $organ->number;
      $address->extension = $organ->extension;
      $address->zip = $organ->zip;
      $address->city = $organ->city;
      $address->country = $organ->country;
      $address->lat = $organ->lat;
      $address->lng = $organ->lng;
      $address->save();
      return $address;
    }

    public function getAddressDetails($delimiter = '<br/>') {
      //for display only, so escaping single quotes to html codes
      $string = "";
      if ($this->address != null) {
        if ($this->address . $this->number) $string .= trim($this->address . " " . $this->number) . $delimiter;
        if ($this->zip . $this->city != "") $string .= $this->zip . " " . $this->city . $delimiter;
        if ($this->extension != "") $string .= $this->extension . $delimiter;
        if ($this->country != "") $string .= __($this->getCountry());
      }
      return displayAsHtml($string);
    }

    public function getDetails($delimiter = '<br/>') {
      //for display only, so escaping single quotes to html codes
      $string = '';
      if ($this->id == 0) {//afhalen
        $string .= $this->contactname . $delimiter;
      }
      else {
        if ($this->getName() != "") {
          $string .= $this->getName() . $delimiter;
        }
        if ($this->contactname != "") {
          $string .= 'T.a.v. ' . $this->contactname . $delimiter;
        }
      }
      return displayAsHtml($string) . $this->getAddressDetails($delimiter);
    }

    public function getName() {
      $organ = Organisation::find_by_id($this->organisation_id);
      return displayAsHtml($organ->name);
    }

    public function getEmail() {
      if ($this->email != '') {
        return $this->email;
      }
      else {
        $organ = Organisation::find_by_id($this->organisation_id);
        if ($organ->email != '' && ValidationHelper::isEmail($organ->email)) {
          return $organ->email;
        }
      }
      return '';
    }

    public function getHighestShippingCat($basket_products) {
      $higestval = 0;
      $shipping_categories = Config::get("shipping_categories");
      foreach ($basket_products as $products) {
        //hack for invoice---
        if ($products instanceof InvoiceProduct) {
          if ($products->product_id == null) {
            continue;
          }
          $prod = Product::find_by_id($products->product_id);
          $products = [];
          $products[] = $prod;
        }
        //---endhack---
        foreach ($products as $product) {
          if (isset($product->shipping_cat) && isset($shipping_categories[$this->country][$product->shipping_cat])) {
            if ($higestval < $shipping_categories[$this->country][$product->shipping_cat]) {
              $higestval = $shipping_categories[$this->country][$product->shipping_cat];
            }
          }
        }
      }

      if (isset($shipping_categories[$this->country])) {
        foreach ($shipping_categories[$this->country] as $key => $sc) {
          if ($sc == $higestval) {
            return $key;
          }
        }
      }
      return 'A';
    }

    public function shippingLimit() {
      if ($this->country == 'nl') {
        return 150;
      }
      return 200;
    }

    public static function getDefaultAddress($organid) {
      return self::find_by(['organisation_id' => $organid, 'is_default' => 1, 'void' => 0]);
    }

    public static function getPickupAddress() {
      return self::find_by_id(0);
    }

    /**
     * Get all addresses known for the given $organid
     *
     * @param int $organid (required)
     * @return OrganisationAddress[]
     */
    public static function getAddresses($organid) {
      return self::find_all_by(['organisation_id' => $organid, 'void' => 0]);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->insertTS = date('Y-m-d H:i:s');
        if (isset($_SESSION['userId'])) {
          $this->insertUser = $_SESSION['userId'];
        }
      }
      $this->updateTS = date('Y-m-d H:i:s');
      if (isset($_SESSION['userId'])) {
        $this->updateUser = $_SESSION['userId'];
      }
      return parent::save($errors);
    }

    public static function exportToExcel() {
      $objPHPExcel = new Spreadsheet();

      $styleArray = [
        'font'      => [
          'bold' => true,
        ],
        'alignment' => [
          'horizontal' => Alignment::HORIZONTAL_LEFT,
        ],
        'borders'   => [
          'top' => [
            'style' => Border::BORDER_THIN,
          ],
        ],
        'fill'      => [
          'type'       => Fill::FILL_GRADIENT_LINEAR,
          'rotation'   => 90,
          'startcolor' => [
            'argb' => 'FFA0A0A0',
          ],
          'endcolor'   => [
            'argb' => 'FFFFFFFF',
          ],
        ],
      ];

      $objPHPExcel->setActiveSheetIndex(0);

      $query = "SELECT oa.*, o.cust_nr, o.name FROM organisation_address oa ";
      $query .= "LEFT JOIN organisation o ON o.id = oa.organisation_id ";
      if (Config::isTrue("USER_MAY_SEE_OWN_ORGANISATION") && $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN) {
        // Mag eigen organisatie zien
        $query .= "AND (o.type != '" . Organisation::TYPE_OTHER . "' OR (o.type='" . Organisation::TYPE_OTHER . "' AND o.id=" . $_SESSION['userObject']->organisation->id . ") ) ";
      }
      elseif ($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN) { // Mag eigen organisatie niet zien
        $query .= " AND o.id!=" . $_SESSION['userObject']->organisation->id . " ";
        if ($_SESSION['userObject']->usergroup != User::USERGROUP_SUPERADMIN && $_SESSION['userObject']->usergroup != User::USERGROUP_ADMIN) {
          $query .= "AND (o.type!='" . Organisation::TYPE_OTHER . "' OR o.type IS NULL) ";
        }
      }
      $query .= "ORDER BY o.id, oa.id";

      $result = DBConn::db_link()->query($query);
      $addresses = [];
      while ($row = $result->fetch_row()) {
        $address = new OrganisationAddress();
        $address->hydrate($row);
        $address->customernr = $row[count(OrganisationAddress::columns)];
        $n = count(OrganisationAddress::columns) + 1;
        $address->name = $row[$n];
        if (!isset($address->organisation_name) || (trim($address->organisation_name) === '')) {
          $address->organisation_name = $address->name;
        }
        $addresses[] = $address;
      }

      $column_names = [
        'Klantnr',
        'Bedrijfsnaam',
        '(Bedrijfs)naam',
        'Contactpersoon',
        'Straat',
        'Huisnr',
        'Postcode',
        'Gemeente',
        'Land',
        'Standaard afleveradres',
      ];

      $coltel = 1;
      foreach ($column_names as $name) {
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel, 1], $name);
        $coltel++;
      }

      $rowtel = 2;
      foreach ($addresses as $address) {
        $coltel = 1;
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->customernr);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->name);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->organisation_name);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->contactname);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->address);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->number);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->zip);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->city);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->country);
        $objPHPExcel->getActiveSheet()->setCellValue([$coltel++, $rowtel], $address->is_default == 1 ? 'j' : 'n');
        $rowtel++;
      }
      $objPHPExcel->getActiveSheet()->getStyle('A1:' . chr(64 + $coltel) . '1')->applyFromArray($styleArray);

      header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      header('Content-Disposition: attachment;filename="Adressen.xlsx"');
      header('Cache-Control: max-age=0');
      $objWriter = IOFactory::createWriter($objPHPExcel, 'Xlsx');

      $objWriter->save('php://output');

    }

  }
