<?php

  use Gsd\Form\Elements\Select;

  AppModel::loadBaseClass('BaseCountry');

  class CountryModel extends BaseCountry {

    public static $countries_cache = null;

    public static $valutacodes = [
      'EUR' => 'Euro',
      'GBP' => 'Brits pond sterling',
      'DKK' => 'Deense kroon',
      'CHF' => 'Zwiterse frank',
    ];

    public static $valutasymbol = [
      'EUR' => '€',
      'GBP' => '£',
      'DKK' => 'DKK',
      'CHF' => 'CHF',
    ];

    public static function getValutaSymbolStat($code) {
      return Country::$valutasymbol[$code];
    }

    public function getValutaSymbol() {
      return Country::$valutasymbol[$this->valutacode];
    }

    /**
     * @return mixed
     */
    public function getCode() {
      return $this->code;
    }

    /**
     * gets the correct valutasymbol
     * @param string $countrycode
     * @return string $valutasymbol
     */
    public static function getValutaSymbolByCountrycode($countrycode) {
      $country = Country::getCountryByCode($countrycode);
      return $country->getValutaSymbolObj();
    }


    public function getName($llang = 'nl') {
      if (isset($this->{'name_' . $llang})) {
        return $this->{'name_' . $llang};
      }

      return $this->name;
    }

    /**
     * Get the ISO 3166-1 alpha-2 country code. The standard for web and structured data
     * @return string
     */
    public function getIsoCountryCode(): string {
      return strtoupper($this->code);
    }

    /**
     * Returns defined or active countries, NOT all countries
     * @return Country[]|null
     * @throws Exception
     */
    public static function getCountries() {
      if (Country::$countries_cache == null) {
        if (Config::isdefined("countries")) {
          Country::$countries_cache = Country::find_all("WHERE code IN ('" . implode("','", array_flip(Config::get("countries"))) . "') ORDER BY pref DESC, name");
        }
        else {
          Country::$countries_cache = Country::find_all_by(['active' => 1], "ORDER BY pref DESC, name");
        }
      }
      return Country::$countries_cache;
    }

    /**
     * @param string $lang
     * @param bool $showglobal
     * @return Country[]
     * @return array|null
     */
    public static function getCountriesSorted($lang = 'nl', $showglobal = false) {

      $countries = Country::getCountries();

      $countries_mapped = AppModel::mapObjectIds(Country::getCountries());
      if (!$showglobal) unset($countries_mapped[35]); //verwijder global

      $sortedc = [];
      foreach ($countries as $id => $land) {
        if ($land->pref) {
          if ($lang == 'nl') {
            $land->name_locale = $land->name_nl;
          }
          elseif ($lang == 'en') {
            $land->name_locale = $land->name;
          }
          else {
            if (Trans::hasValue($land->name_nl)) {
              $land->name_locale = __($land->name_nl);
            }
            else {
              $land->name_locale = __($land->name);
            }
          }

          unset($countries_mapped[$land->id]);
          if (!$showglobal && $land->id == 35) {
            //global niet tonen
          }
          else {
            $sortedc[] = $land;
          }
        }
        else {
          break;
        }
      }
      usort($sortedc, "Country::sortCountryLocale");

      foreach ($countries_mapped as $k => $c) {
        if ($lang == 'nl') {
          $countries_mapped[$k]->name_locale = $c->name_nl;
        }
        elseif ($lang == 'en') {
          $countries_mapped[$k]->name_locale = $c->name;
        }
        else {
          $countries_mapped[$k]->name_locale = __($c->name);
        }

      }

      usort($countries_mapped, "Country::sortCountryLocale");
      $sortedc = array_merge($sortedc, $countries_mapped);


      return $sortedc;

    }

    /**
     * Get countrie names sorted as array
     * @param string $lang
     * @return array
     */
    public static function getCountriesSortedAr(string $lang = 'nl'): array {
      $cs = [];
      foreach (Country::getCountriesSorted() as $c) {
        if ($lang == 'nl') {
          $cs[$c->code] = $c->name_nl;
        }
        else {
          $cs[$c->code] = $c->name;
        }
      }
      return $cs;
    }


    //   public static function getCountriesSortedForm() {
    //     $countries_mapped = AppModel::mapObjectIds(Country::getCountries());
    //     unset($countries_mapped[35]); //verwijder global
    //     $sortedc = array();

    //     $landen = array(
    //         1=>"Nederland",
    //         2=>"Belgie",
    //         3=>"Duitsland",
    //         4=>"Luxemburg",
    //         5=>"Oostenrijk",
    //         6=>"Frankrijk",
    //         9=>"Zwitserland",
    //         10=>"Denemarken",
    //     );

    //     foreach($landen as $id=>$land) {
    //       $sortedc[] = $countries_mapped[$id];
    //       unset($countries_mapped[$id]);
    //     }

    //     usort($sortedc, "Country::sortCountry");
    //     usort($countries_mapped, "Country::sortCountry");

    //     $sortedc = array_merge($sortedc,$countries_mapped);

    //     return $sortedc;

    //   }


    public static function isEu($id) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->id == $id) {
          if ($country->eu == 1) {
            return true;
          }
          return false;
        }
      }
      return false;
    }

    public static function getCountryNameById($id, $llang = '') {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->id == $id) {
          if ($llang != '' && isset($country->{'name_' . $llang})) {
            return $country->{'name_' . $llang};
          }
          else {
            return $country->name;
          }
        }
      }
      return '';
    }

    /**
     * Get code by id
     * @param $id
     * @return mixed
     */
    public static function getCountryCodeById($id) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->id == $id) return $country->code;
      }
      return false;
    }

    /**
     * Get country name by code
     * @param $code
     * @return string
     */
    public static function getCountryNameByCode($code) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->code == $code) return $country->name;
      }
      return "";
    }

    /**
     * Get country dutch name by code
     * @param $code
     * @return string
     */
    public static function getCountryNameNlByCode($code) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->code == $code) return $country->name_nl;
      }
      return "";
    }

    /**
     * @param string $code
     * @return Country
     */
    public static function getCountryByCode($code) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->code == $code) return $country;
      }
      return null;
    }

    /**
     * @param $id
     * @return Country
     */
    public static function getCountryById($id) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->id == $id) return $country;
      }
      return null;
    }

    /**
     * Get id by code
     * @param $countrycode
     * @return mixed
     */
    public static function getCountryIdByCountryCode($countrycode) {
      $countries = Country::getCountries();
      foreach ($countries as $country) {
        if ($country->code == $countrycode) return $country->id;
      }
      return false;
    }


    public static function sortLand($a, $b) {
      return strcmp($a["naam"], $b["naam"]);
    }

    public static function sortCountry($a, $b) {
      return strcmp($a->name, $b->name);
    }

    public static function sortCountryLocale($a, $b) {
      return strcmp($a->name_locale, $b->name_locale);
    }

    /**
     * Bepaal taal a.d.h.v. land id
     * @param $land_id
     * @return string
     */
    public static function determineLang($land_id) {
      $lang = 'nl';
      if ($land_id == 3 || $land_id == 4) {
        $lang = 'de';
      }
      elseif ($land_id == 10) {
        $lang = 'da';
      }
      elseif ($land_id == 8) {
        $lang = 'en';
      }
      elseif ($land_id == 6) {
        $lang = 'fr';
      }
      return $lang;
    }

    /**
     * Fill a GsdForm country select with options
     * @param Select $select
     * @param bool $mapbyid : set country id instead of country code in value
     * @return Select
     */
    public static function setSelectOptions($select, $mapbyid = false) {
      $lang = 'nl';
      if (isset($_SESSION['lang'])) $lang = $_SESSION['lang'];

      $landen = Country::getCountriesSorted($lang);
      $checkval = "";

      $select->addOptionHelper("", __("Selecteer land.."));
      $pref = 1;
      foreach ($landen as $land) {
        if ($land->pref != $pref) {
          $spacer = new \Gsd\Form\Elements\Option("-", "-----------------------------------------------");
          $spacer->setDisabled(true);
          $select->addOption($spacer);
        }
        $key = $land->code;
        if ($mapbyid) {
          $key = $land->id;
        }

        $text = __($land->name);
        if (isset($land->name_locale)) {
          $text = $land->name_locale;
        }
        elseif ($lang == 'en') { //name is in english
          $text = $land->name;
        }
        $select->addOptionHelper($key, $text);
        $pref = $land->pref;
      }
      return $select;
    }

    public static function getCountrySelect($name, $post = null, $class = "", $style = "", $disabled = false, $required = false, $mapbyid = false) {
      $lang = 'nl';
      if (isset($_SESSION['lang'])) $lang = $_SESSION['lang'];

      $landen = Country::getCountriesSorted($lang);
			$disabled = $disabled || (count($landen) == 1);
      $checkval = "";
      if (is_object($post)) {
        if (isset($post->{$name})) $checkval = $post->{$name};
      }
      elseif (is_array($post)) {
        if (isset($post[$name])) $checkval = $post[$name];
      }
      else {
        $checkval = $post;
      }

      $out = '<select name="' . $name . '" id="' . $name . '" class="' . $class . '" style="' . $style . '" ' . ($disabled ? ' disabled="disabled"' : '') . ' ' . ($required ? ' required' : '') . '>';
      $out .= '<option value="">' . __('Selecteer land') . '...</option>';
      $pref = 1;
      foreach ($landen as $land) {
        if ($land->pref != $pref) {
          $out .= '<option value="" disabled="disabled">-----------------------------------------------</option>';
        }
        $key = $land->code;
        if ($mapbyid) {
          $key = $land->id;
        }
        $out .= '<option value="' . $key . '" ';
        if ($checkval == $key) {
          $out .= ' selected="selected"';
        }
        $out .= '>';
        if (isset($land->name_locale)) {
          $out .= $land->name_locale;
        }
        elseif ($lang == 'en') { //name is in english
          $out .= $land->name;
        }
        else {
          $out .= __($land->name);
        }
        $out .= '</option>';
        $pref = $land->pref;
      }
      $out .= '</select>';
      return $out;
    }

    public static function getCountrySelectId($name, $post = null, $class = "", $style = "", $disabled = false, $required = false) {
      return self::getCountrySelect($name, $post, $class, $style, $disabled, $required, true);
    }

    public function getCountryInvoiceText() {
      if (!Config::isdefined("USER_INVOICE_TEXTS")) {
        return "";
      }

      $invoice_text = Config::get("USER_INVOICE_TEXTS");
      if (isset($invoice_text[$this->code])) {
        return $invoice_text[$this->code];
      }
      elseif ($this->eu == 1) {
        return $invoice_text['eu'];
      }
      else {
        if (in_array($this->code, $invoice_text['eu_outside'])) {
          return $invoice_text['eu_outside_txt'];
        }
        else {
          return $invoice_text['other'];
        }
      }
    }

    /**
     * Format number as custom in the specified country
     *
     * @param double $price (required)
     * @param Country|null $country (optional) default: null
     * @param bool $showvaluta (optional) default: true
     * @param int $number_decimals (optional) default: 2
     * @return string
     */
    public static function formatNumber($price, ?Country $country = null, bool $showvaluta = true, int $number_decimals = 2): string {
      $str = '';
      if ($showvaluta && $country) {
        $str .= $country->getValutaSymbol() . ' ';
      }
      $decimal_delimiter = ',';
      $thousand_delimiter = '.';
      if ($country != null && ($country->code == 'gb' || $country->code == 'us')) {
        $decimal_delimiter = '.';
        $thousand_delimiter = ',';
      }
      $str .= number_format($price, $number_decimals, $decimal_delimiter, $thousand_delimiter);

      return $str;
    }

    /**
     * Land bepaald addres format
     * Return adressformat: nl | reversed
     * @return string
     */
    public function getAddressformat() {
      $formats = Config::get("COUNTRY_ADDRESS_FORMAT");
      foreach ($formats as $k => $format) {
        if (in_array($this->code, $format)) {
          return $k;
        }
      }
      return 'nl';
    }

    /**
     * Get vats as array
     * @return array
     */
    public function getVats() {
      $vats = [];
      if ($this->vat_low != "") $vats["low"] = $this->vat_low;
      if ($this->vat_middle != "") $vats["middle"] = $this->vat_middle;
      if ($this->vat_high != "") $vats["high"] = $this->vat_high;
      return $vats;
    }


  }
