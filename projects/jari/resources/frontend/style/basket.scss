.basket-items > div {
  display: block;
}

.basket-products {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.basket-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.product-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  @media (max-width: 768px) {
    align-self: center;
    width: 120px;
    height: 120px;
  }
}

.product-info {
  flex-grow: 1;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
  }
}

.quantity-info p {
  margin: 0;
}

.basket-actions {
  flex-shrink: 0;

  @media (max-width: 768px) {
    align-self: stretch;
  }
}

.basket-quotation {
  margin-top: 2rem;
  text-align: right;
}

.basket-addresses {
  border-collapse: collapse;
  tr {
    border-bottom: 1px solid #ddd !important;
  }
  th {
    text-align: left;
  }
}