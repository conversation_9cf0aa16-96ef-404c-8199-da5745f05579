<?php

  AppModel::loadModelClass('CategoryModel');

  class Category extends CategoryModel {

    /**
     * Get the category tree, with content, sorted.
     * @param $language
     * @param bool $showAll
     * @return Category[]
     */
    public static function getTreeWithContent($language = "nl", bool $showAll = false) {

      $allCategories = Category::getAll($language, !$showAll);
      foreach ($allCategories as $key => $cat) {
        foreach ($allCategories as $pkey => $searchparent) {
          if ($searchparent->id == $cat->parent_id) {
            $allCategories[$pkey]->children[$cat->id] = $cat;
          }
        }
      }

      //opruimen niet root cats
      foreach ($allCategories as $key => $cat) {
        if ($cat->parent_id != null) {
          unset($allCategories[$key]);
        }
      }

      self::sortTree($allCategories);

      return $allCategories;
    }

    /**
     * Sort a tree on property sort
     * @param Category[] $allCategories
     * @return void
     */
    private static function sortTree(&$allCategories) {
      uasort($allCategories, function ($a, $b) {
        if ($a->sort == $b->sort) return 0;
        return $a->sort > $b->sort ? 1 : -1;
      });
      foreach ($allCategories as $cat) {
        if (isset($cat->children) && count($cat->children) > 0) {
          self::sortTree($cat->children);
        }
      }
    }

    public static function getByUid($uid) {
      $query = "SELECT category.* FROM category ";
      $query .= "JOIN jari_category_uid ON jari_category_uid.category_id = category.id AND jari_category_uid.abo_uid = '$uid'";
      $result = DBConn::db_link()->query($query);
      $data = $result->fetch_row();

      return ($data === null) ? null : (new self())->hydrateNext($data);
    }

    /**
     * @param $data
     * @param $parentId
     * @return Category|null
     * @throws GsdDbException
     * @throws GsdException
     */
    public static function createFromAboGroupData($data, $uid, $parentId = null): ?Category {
      if (self::getByUid($uid)) return null;

      $category = new self([
        'online_custshop' => 1,
        'void' => 0,
      ]);
      if ($parentId) $category->parent_id = $parentId;
      $category->save();

      $category->uid = (new JariCategoryUid([
        'category_id' => $category->id,
        'abo_uid' => $uid,
      ]));
      $category->uid->save();
      $categoryContent = new CategoryContent();
      $categoryContent->locale = 'nl';
      $categoryContent->name = !empty($data['omschr']) ? ucfirst($data['omschr']) : $uid;
      $categoryContent->buildDefaultUrl($category);
      $categoryContent->buildUrlUnique();
      $categoryContent->category_id = $category->id;
      $categoryContent->url = str_replace("--ID--", $category->id, $categoryContent->url);

      $categoryContent->save();

      $category->content = $categoryContent;

      return $category;
    }

    public function setImageUrl(): void {
      if ($this->foto_orig) {
        $this->imageUrl = URL_UPLOADS . 'images/catalog/' . $this->foto_orig;
      } else {
        $this->imageUrl = URL_TEMPLATE . "frontend/images/default-thumbnail.jpg";
      }
    }
  }