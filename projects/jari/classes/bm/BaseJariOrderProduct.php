<?php
class BaseJariOrderProduct extends AppModel {

  const DB_NAME = '';
  const TABLE_NAME = 'jari_order_product';
  const OM_CLASS_NAME = 'JariOrderProduct';
  const columns = ['id', 'jari_order_id', 'product_id', 'size'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'jari_order_id'               => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'product_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'size'                        => ['type' => 'decimal', 'length' => '10,2', 'null' => false],
  ];

  protected static array $primary_key = ['id'];
  protected string $auto_increment = 'id';

  public $id, $jari_order_id, $product_id, $size;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return JariOrderProduct[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return JariOrderProduct[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return JariOrderProduct[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return JariOrderProduct|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): JariOrderProduct|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return JariOrderProduct|false
   */
  public static function find_by_id($id, string $raw_sql = ''): JariOrderProduct|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}