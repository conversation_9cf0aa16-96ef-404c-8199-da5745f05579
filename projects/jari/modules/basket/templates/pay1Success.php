<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Afleveradres selecteren</h1>
    </div>
  </section>

  <section class="gsd-container">
    <?php writeErrors($errors); ?>

    <?php if (!isset($_GET['add_new']) || $_GET['add_new'] != '1'): ?>
      <!-- Show existing addresses -->
      <?php if (!empty($shippingaddresses)): ?>
        <form method="post" class="application-form">
          <h2>Kies een bestaand afleveradres</h2>
          <?php foreach ($shippingaddresses as $address): ?>
            <div class="form-group">
              <label>
                <input type="radio" name="address_id" value="<?php echo $address->id ?>" required>
                <strong><?php echo htmlspecialchars($address->contactname) ?></strong>
                <?php if (!empty($address->organisation_name)): ?>
                  <br><?php echo htmlspecialchars($address->organisation_name) ?>
                <?php endif; ?>
                <br><?php echo htmlspecialchars($address->address . ' ' . $address->number) ?>
                <br><?php echo htmlspecialchars($address->zip . ' ' . $address->city) ?>
                <?php if (!empty($address->country)): ?>
                  <br><?php echo htmlspecialchars(strtoupper($address->country)) ?>
                <?php endif; ?>
              </label>
            </div>
          <?php endforeach; ?>

          <div class="form-group">
            <button type="submit" name="select_address" class="btn-primary">
              Gebruik geselecteerd adres
            </button>
            <a href="<?php echo reconstructQuery(['action']) ?>action=pay1&add_new=1" class="btn-secondary" style="margin-left: 1rem;">
              Nieuw adres toevoegen
            </a>
          </div>
        </form>
      <?php else: ?>
        <h2>Voeg een afleveradres toe</h2>
        <p>U heeft nog geen afleveradressen. Voeg er een toe om door te gaan.</p>
      <?php endif; ?>

    <?php else: ?>
      <!-- Show new address form -->
      <form method="post" class="application-form">
        <h2><?php echo !empty($shippingaddresses) ? 'Nieuw afleveradres toevoegen' : 'Voeg een afleveradres toe' ?></h2>

        <div class="form-group">
          <label for="contactname">Contactpersoon *</label>
          <input
            class="form-input <?php echo isset($errors['contactname']) ? 'inputerror' : '' ?>"
            name="contactname"
            type="text"
            id="contactname"
            placeholder="Naam contactpersoon"
            required
            value="<?php echo htmlspecialchars($_POST['contactname'] ?? '') ?>"
          />
        </div>

        <div class="form-group">
          <label for="organisation_name">Bedrijfsnaam (optioneel)</label>
          <input
            class="form-input <?php echo isset($errors['organisation_name']) ? 'inputerror' : '' ?>"
            name="organisation_name"
            type="text"
            id="organisation_name"
            placeholder="Bedrijfsnaam"
            value="<?php echo htmlspecialchars($_POST['organisation_name'] ?? '') ?>"
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="address">Straat *</label>
            <input
              class="form-input <?php echo isset($errors['address']) ? 'inputerror' : '' ?>"
              name="address"
              type="text"
              id="address"
              placeholder="Straatnaam"
              required
              value="<?php echo htmlspecialchars($_POST['address'] ?? '') ?>"
            />
          </div>
          <div class="form-group">
            <label for="number">Huisnummer *</label>
            <input
              class="form-input <?php echo isset($errors['number']) ? 'inputerror' : '' ?>"
              name="number"
              type="text"
              id="number"
              placeholder="Nr."
              required
              value="<?php echo htmlspecialchars($_POST['number'] ?? '') ?>"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="zip">Postcode *</label>
            <input
              class="form-input <?php echo isset($errors['zip']) ? 'inputerror' : '' ?>"
              name="zip"
              type="text"
              id="zip"
              placeholder="1234AB"
              required
              value="<?php echo htmlspecialchars($_POST['zip'] ?? '') ?>"
            />
          </div>
          <div class="form-group">
            <label for="city">Plaats *</label>
            <input
              class="form-input <?php echo isset($errors['city']) ? 'inputerror' : '' ?>"
              name="city"
              type="text"
              id="city"
              placeholder="Plaatsnaam"
              required
              value="<?php echo htmlspecialchars($_POST['city'] ?? '') ?>"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="country">Land *</label>
          <select class="form-input" name="country" id="country" required>
            <option value="nl" <?php echo ($_POST['country'] ?? 'nl') === 'nl' ? 'selected' : '' ?>>Nederland</option>
            <option value="be" <?php echo ($_POST['country'] ?? '') === 'be' ? 'selected' : '' ?>>België</option>
            <option value="de" <?php echo ($_POST['country'] ?? '') === 'de' ? 'selected' : '' ?>>Duitsland</option>
          </select>
        </div>

        <div class="form-group">
          <button type="submit" name="create_address" class="btn-primary">
            Adres toevoegen en gebruiken
          </button>
          <?php if (!empty($shippingaddresses)): ?>
            <a href="<?php echo reconstructQuery(['action', 'add_new']) ?>action=pay1" class="btn-secondary" style="margin-left: 1rem;">
              Annuleren
            </a>
          <?php endif; ?>
          <a href="<?php echo reconstructQuery(['action']) ?>action=list" style="margin-left: 1rem;">
            Terug naar winkelwagen
          </a>
        </div>
      </form>
    <?php endif; ?>
  </section>
</div>



