<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Afleveradres selecteren</h1>
    </div>
  </section>
</div>

<div class="application-form">
  <?php if (!empty($errors)): ?>
    <div class="alert">
      <?php foreach ($errors as $error): ?>
        <p><?php echo htmlspecialchars($error) ?></p>
      <?php endforeach; ?>
    </div>
  <?php endif; ?>

  <?php if (!empty($shippingaddresses)): ?>
    <div class="form-group">
      <h2>Kies een bestaand afleveradres</h2>
      <form method="post" class="address-selection-form">
        <?php foreach ($shippingaddresses as $address): ?>
          <div class="address-option">
            <label class="address-label">
              <input type="radio" name="address_id" value="<?php echo $address->id ?>" required>
              <div class="address-details">
                <?php if (!empty($address->organisation_name)): ?>
                  <br><?php echo htmlspecialchars($address->organisation_name) ?>
                <?php endif; ?>
                <br><?php echo htmlspecialchars($address->address . ' ' . $address->number) ?>
                <br><?php echo htmlspecialchars($address->zip . ' ' . $address->city) ?>
                <?php if (!empty($address->country)): ?>
                  <br><?php echo htmlspecialchars(strtoupper($address->country)) ?>
                <?php endif; ?>
              </div>
            </label>
          </div>
        <?php endforeach; ?>

        <div class="form-group">
          <button type="submit" name="select_address" class="btn-primary">
            Gebruik geselecteerd adres
          </button>
        </div>
      </form>
    </div>

    <div class="form-group">
      <h2>Of voeg een nieuw afleveradres toe</h2>
    </div>
  <?php else: ?>
    <div class="form-group">
      <h2>Voeg een afleveradres toe</h2>
      <p>U heeft nog geen afleveradressen. Voeg er een toe om door te gaan.</p>
    </div>
  <?php endif; ?>

  <form method="post" class="new-address-form">
    <div class="form-row">
      <div class="form-group">
        <label for="contactname">Contactpersoon *</label>
        <input
          class="form-input <?php echo isset($errors['contactname']) ? 'inputerror' : '' ?>"
          name="contactname"
          type="text"
          id="contactname"
          placeholder="Naam contactpersoon"
          required
          value="<?php echo htmlspecialchars($_POST['contactname'] ?? '') ?>"
        />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="address">Straat *</label>
        <input
          class="form-input <?php echo isset($errors['address']) ? 'inputerror' : '' ?>"
          name="address"
          type="text"
          id="address"
          placeholder="Straatnaam"
          required
          value="<?php echo htmlspecialchars($_POST['address'] ?? '') ?>"
        />
      </div>
      <div class="form-group">
        <label for="number">Huisnummer *</label>
        <input
          class="form-input <?php echo isset($errors['number']) ? 'inputerror' : '' ?>"
          name="number"
          type="text"
          id="number"
          placeholder="Nr."
          required
          value="<?php echo htmlspecialchars($_POST['number'] ?? '') ?>"
        />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="zip">Postcode *</label>
        <input
          class="form-input <?php echo isset($errors['zip']) ? 'inputerror' : '' ?>"
          name="zip"
          type="text"
          id="zip"
          placeholder="1234AB"
          required
          value="<?php echo htmlspecialchars($_POST['zip'] ?? '') ?>"
        />
      </div>
      <div class="form-group">
        <label for="city">Plaats *</label>
        <input
          class="form-input <?php echo isset($errors['city']) ? 'inputerror' : '' ?>"
          name="city"
          type="text"
          id="city"
          placeholder="Plaatsnaam"
          required
          value="<?php echo htmlspecialchars($_POST['city'] ?? '') ?>"
        />
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="country">Land *</label>
        <select class="form-input" name="country" id="country" required>
          <option value="nl" <?php echo ($_POST['country'] ?? 'nl') === 'nl' ? 'selected' : '' ?>>Nederland</option>
          <option value="be" <?php echo ($_POST['country'] ?? '') === 'be' ? 'selected' : '' ?>>België</option>
          <option value="de" <?php echo ($_POST['country'] ?? '') === 'de' ? 'selected' : '' ?>>Duitsland</option>
        </select>
      </div>
    </div>

    <div class="form-group">
      <button type="submit" name="create_address" class="btn-primary">
        Nieuw adres toevoegen en gebruiken
      </button>
    </div>
  </form>

  <div class="form-group">
    <a href="<?php echo PageMap::getUrl('M_BASKET') ?>" class="btn-secondary">
      Terug naar winkelwagen
    </a>
  </div>
</div>

<style>
.address-option {
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  transition: border-color 0.2s ease;
}

.address-option:hover {
  border-color: var(--color-primary);
}

.address-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: normal;
  margin-bottom: 0;
}

.address-label input[type="radio"] {
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.address-details {
  flex-grow: 1;
  line-height: 1.4;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  padding: 1.3em;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
  color: white;
  text-decoration: none;
}

.new-address-form {
  border-top: 1px solid #ddd;
  padding-top: 2rem;
  margin-top: 2rem;
}

.address-selection-form {
  margin-bottom: 2rem;
}
</style>
