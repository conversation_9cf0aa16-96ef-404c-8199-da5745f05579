<?php
  /* @var $shippingaddresses array */
  /* @var $errors array */
?>

<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Afleveradres selecteren</h1>
    </div>
  </section>

  <section class="gsd-container">
    <?php if (!isset($_GET['add_new']) || $_GET['add_new'] != '1'): ?>
      <form method="post" class="application-form">
        <?php writeErrors($errors); ?>
        <h2>Kies een bestaand afleveradres</h2>
        <?php foreach ($shippingaddresses as $address): ?>
          <div class="form-group">
            <label>
              <input type="radio" name="address_id" value="<?php echo $address->id ?>" required>
              <strong><?php echo htmlspecialchars($address->contactname ?? '') ?></strong>
              <?php if (!empty($address->organisation_name ?? '')): ?>
                <br><?php echo htmlspecialchars($address->organisation_name ?? '') ?>
              <?php endif; ?>
              <br><?php echo htmlspecialchars($address->address . ' ' . $address->number) ?>
              <br><?php echo htmlspecialchars($address->zip . ' ' . $address->city) ?>
              <?php if (!empty($address->country ?? '')): ?>
                <br><?php echo htmlspecialchars(strtoupper($address->country ?? '')) ?>
              <?php endif; ?>
            </label>
          </div>
        <?php endforeach; ?>

        <div class="form-group">
          <button type="submit" name="select_address" class="btn-primary">
            Gebruik geselecteerd adres
          </button>
          <a href="<?php echo reconstructQueryAdd(['action' => 'pay1', 'add_new' => '1']) ?>" class="btn-secondary">
            Nieuw adres toevoegen
          </a>
          <a href="<?php echo reconstructQueryAdd()?>" style="margin-left: 1rem;">
            Terug naar winkelwagen
          </a>
        </div>
      </form>

    <?php else: ?>
      <form method="post" class="application-form">
        <h2><?php echo !empty($shippingaddresses) ? 'Nieuw afleveradres toevoegen' : 'Voeg een afleveradres toe' ?></h2>
        <?php writeErrors($errors); ?>
        <div class="form-group">
          <label for="organisation_name">Bedrijfsnaam *</label>
          <input
            class="form-input"
            name="organisation_name"
            type="text"
            id="organisation_name"
            placeholder="Bedrijfsnaam"
            value="<?php echo $_POST['organisation_name'] ?? $_SESSION['userObject']?->organisation?->name ?? '' ?>"
          />
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="address">Straat *</label>
            <input
              class="form-input"
              name="address"
              type="text"
              id="address"
              placeholder="Straatnaam"
              value="<?php writeIfSet('address') ?>"
            />
          </div>
          <div class="form-group">
            <label for="number">Huisnummer *</label>
            <input
              class="form-input"
              name="number"
              type="text"
              id="number"
              placeholder="Nr."
              value="<?php writeIfSet('number') ?>"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="zip">Postcode *</label>
            <input
              class="form-input"
              name="zip"
              type="text"
              id="zip"
              placeholder="1234AB"
              value="<?php writeIfSet('zip') ?>"
            />
          </div>
          <div class="form-group">
            <label for="city">Plaats *</label>
            <input
              class="form-input"
              name="city"
              type="text"
              id="city"
              placeholder="Plaatsnaam"
              value="<?php writeIfSet('city') ?>"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="country">Land *</label>
          <?php echo Country::getCountrySelect('country', $_POST['country'] ?? 'nl', 'form-input') ?>
        </div>

        <div class="form-group">
          <label for="contactname">Contactpersoon</label>
          <input
            class="form-input"
            name="contactname"
            type="text"
            id="contactname"
            placeholder="Naam contactpersoon"
            value="<?php writeIfSet('contactname') ?>"
          />
        </div>

        <div class="form-group">
          <button type="submit" name="create_address" class="btn-primary">
            Adres toevoegen en gebruiken
          </button>
          <?php if (!empty($shippingaddresses)): ?>
            <a href="<?php echo reconstructQuery(['action', 'add_new']) ?>action=pay1" class="btn-secondary" style="margin-left: 1rem;">
              Annuleren
            </a>
          <?php endif; ?>
          <a href="<?php echo reconstructQueryAdd()?>" style="margin-left: 1rem;">
            Terug naar winkelwagen
          </a>
        </div>
      </form>
    <?php endif; ?>
  </section>
</div>



