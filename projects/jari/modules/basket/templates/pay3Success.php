<?php
  /* @var $basket array */
  /* @var $shippingAddress OrganisationAddress */
  /* @var $errors array */
?>

<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Uw Bestelling</h1>
    </div>
  </section>

  <section class="gsd-container">
    <form method="post" class="application-form">
      <?php writeErrors($errors); ?>

      <div class="basket-products">
        <?php foreach($basket['items'] as $item): ?>
        <div class="basket-item">
          <div class="product-image">
            <img src="<?php echo $item['product']->container->imageUrl ?>" alt="<?php echo htmlspecialchars($item['product']->content->name) ?>" />
          </div>
          <div class="product-info">
            <h3><?php echo $item['product']->content->name ?></h3>
            <p>Code: <?php echo $item['product']->code ?></p>
          </div>
          <div class="quantity-info">
            <p>Aantal: <?php echo $item['quantity'] ?></p>
          </div>
        </div>
        <?php endforeach; ?>
      </div>

      <h3 style="margin: 0;">Afleveradres</h3>
      <div class="basket-shipping-address">
        <?php if (!empty($shippingAddress->contactname)): ?>
          <strong><?php echo htmlspecialchars($shippingAddress->contactname) ?></strong><br>
        <?php endif; ?>
        <?php if (!empty($shippingAddress->organisation_name)): ?>
          <?php echo htmlspecialchars($shippingAddress->organisation_name) ?><br>
        <?php endif; ?>
        <?php echo $shippingAddress->getAddressDetails() ?>
      </div>

      <div class="center-between" style="margin-top: 2rem;">
        <a href="<?php echo reconstructQueryAdd(['action' => 'pay1']) ?>">
          Terug
        </a>
        <button type="submit" name="place_order" class="btn-primary">
          Offerte aanvragen
        </button>
      </div>
    </form>
  </section>
</div>
