<?php
  /* @var $basket array */
  /* @var $shippingAddress OrganisationAddress */
  /* @var $errors array */
?>

<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Uw Bestelling</h1>
    </div>
  </section>

  <section class="gsd-container">
    <form method="post" class="application-form">
      <?php writeErrors($errors); ?>

      <h2>Uw bestelling</h2>
      <?php if (!empty($basket['items'])): ?>
        <div class="basket-products">
          <?php foreach($basket['items'] as $item): ?>
          <div class="basket-item">
            <div class="product-image">
              <img src="<?php echo $item['product']->container->imageUrl ?>" alt="<?php echo htmlspecialchars($item['product']->content->name) ?>" />
            </div>
            <div class="product-info">
              <h3><?php echo $item['product']->content->name ?></h3>
              <p>Code: <?php echo $item['product']->code ?></p>
            </div>
            <div class="quantity-info">
              <p>Aantal: <?php echo $item['quantity'] ?></p>
            </div>
          </div>
          <?php endforeach; ?>
        </div>
      <?php else: ?>
        <p>Uw winkelwagen is leeg.</p>
      <?php endif; ?>

      <h3>Afleveradres</h3>
      <?php if (!empty($shippingAddress)): ?>
        <div class="shipping-address">
          <?php if (!empty($shippingAddress->contactname)): ?>
            <strong><?php echo htmlspecialchars($shippingAddress->contactname) ?></strong><br>
          <?php endif; ?>
          <?php if (!empty($shippingAddress->organisation_name)): ?>
            <?php echo htmlspecialchars($shippingAddress->organisation_name) ?><br>
          <?php endif; ?>
          <?php echo $shippingAddress->getAddressDetails() ?>
        </div>
      <?php else: ?>
        <p class="error">Geen afleveradres geselecteerd.</p>
      <?php endif; ?>

      <div class="center-between" style="margin-top: 2rem;">
        <a href="<?php echo reconstructQueryAdd(['action' => 'pay1']) ?>">
          Terug
        </a>
        <button type="submit" name="place_order" class="btn-primary">
          Offerte aanvragen
        </button>
      </div>
    </form>
  </section>
</div>

<style>
.basket-products {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.basket-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
}

.product-image {
  flex-shrink: 0;
}

.product-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.product-info {
  flex: 1;
}

.product-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.product-info p {
  margin: 0;
  color: #666;
}

.quantity-info {
  flex-shrink: 0;
  text-align: right;
}

.quantity-info p {
  margin: 0;
  font-weight: bold;
}

.shipping-address {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.center-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
