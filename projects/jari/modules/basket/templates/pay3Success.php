<div class="gsd-editor">
  <section class="gsd-container gsd-wysiwyg-image header-block small-block">
    <div>
      <h1>Uw Bestelling</h1>
    </div>
  </section>

  <section class="gsd-container">
    <form method="post" class="application-form">
      <?php writeErrors($errors); ?>

      <?php if (!empty($basket['items'])): ?>
        <table class="basket-products">
          <thead>
            <tr>
              <th><strong>Product</strong></th>
              <th><strong>Aantal</strong></th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($basket['items'] as $item): ?>
              <tr>
                <td>
                  <div class="product-info">
                    <?php if (!empty($item['product']->container->imageUrl)): ?>
                      <img src="<?php echo $item['product']->container->imageUrl ?>" alt="<?php echo htmlspecialchars($item['product']->content->name) ?>" class="product-image">
                    <?php endif; ?>
                    <div class="product-details">
                      <strong><?php echo htmlspecialchars($item['product']->content->name) ?></strong>
                      <br><small>Code: <?php echo htmlspecialchars($item['product']->code) ?></small>
                    </div>
                  </div>
                </td>
                <td><?php echo $item['quantity'] ?></td>
              </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      <?php else: ?>
        <p>Uw winkelwagen is leeg.</p>
      <?php endif; ?>

      <!-- Shipping Address -->
      <h2>Afleveradres</h2>
      <?php if (!empty($shippingAddress)): ?>
        <div class="shipping-address">
          <strong><?php echo htmlspecialchars($shippingAddress->contactname ?? '') ?></strong>
          <?php if (!empty($shippingAddress->organisation_name)): ?>
            <br><?php echo htmlspecialchars($shippingAddress->organisation_name) ?>
          <?php endif; ?>
          <br><?php echo $shippingAddress->getAddressDetails() ?>
        </div>
      <?php else: ?>
        <p class="error">Geen afleveradres geselecteerd.</p>
      <?php endif; ?>

      <!-- Action Buttons -->
      <div class="center-between" style="margin-top: 2rem;">
        <a href="<?php echo reconstructQueryAdd(['action' => 'pay1']) ?>">
          Terug naar adres selectie
        </a>
        <button type="submit" name="place_order" class="btn-primary">
          Bestelling plaatsen
        </button>
      </div>
    </form>
  </section>
</div>

<style>
.basket-products {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.basket-products th,
.basket-products td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.basket-products th {
  background-color: #f8f9fa;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.product-details {
  flex: 1;
}

.shipping-address {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.center-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
