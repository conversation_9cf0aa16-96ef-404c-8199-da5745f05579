<?php

  use domain\basket\service\BasketService;

  class basketJariActions extends basketActions {

    public function preExecute() {
      parent::preExecute();
      $this->showBreadcrumbs = count(BreadCrumbs::getInstance()->getItems()) > 1;
    }

    /*
     * Nog niet ingelogd of een nieuwe gebruiker aanmaken
     */
    public function executePay0(): void {
      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem('Inloggen');

      $this->errors = [];
      if (isset($_POST['go_login'])) {
        $this->handleLogin();
      }
      elseif (isset($_POST['go_forgotten'])) {
        $this->errors = array_merge($this->errors, $this->sendPasswordForgotten());
      }

      $this->seo_title = "Inloggen of een nieuwe account maken";
    }

    private function handleLogin(): void {
      $errors = $this->validateLoginInput($_POST['emailadres'] ?? '', $_POST['password'] ?? '');

      if (empty($errors)) {
        $ignorePassword = $this->shouldIgnorePassword($_POST['password']);
        $user = User::login(trim($_POST['emailadres']), trim($_POST['password']), $ignorePassword, Site::getFrontendLoginUsergroups(), null, true);

        if ($user) {
          $this->processLoginResult($user);
        }
        else {
          $errors[] = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');
        }
      }
      $this->errors = array_merge($this->errors, $errors);
    }

    private function validateLoginInput(string $email, string $password): array {
      $errors = [];

      if (empty($email) || !ValidationHelper::isEmail($email)) {
        $errors[] = 'E-mailadres leeg of ongeldig';
      }
      if (empty($password)) {
        $errors[] = 'Wachtwoord is leeg';
      }

      return $errors;
    }

    private function shouldIgnorePassword(string $password): bool {
      return Config::isdefined("MASTER_PASSWORD") && $password === Config::get("MASTER_PASSWORD");
    }

    private function processLoginResult($user): void {
      if ($user->maylogin == 1) {
        GsdSession::startSession($user, 'frontend');

        $message = 'U bent succesvol ingelogd.';
        if (!empty($_POST['redirectto'])) {
          ResponseHelper::redirectMessage($message, urldecode($_POST['redirectto']));
        }
        else {
          ResponseHelper::redirectMessage($message);
        }
      }
      elseif ($user->maylogin == 0) {
        $this->errors[] = __('Uw account is nog niet geactiveerd door onze medewerkers.');
      }
    }

    public function executePasswordreset(): void {
      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem('Wachtwoord resetten');

      if (!isset($_GET["hash"]) || !isset($_GET["id"])) {
        ResponseHelper::redirect("/");
      }
      $user = User::find_by_id($_GET["id"]);
      if (!$user) {
        ResponseHelper::redirectNotFound();
      }

      $up = UserProfile::getUPByUserIdAndCode($user->id, "pw-reset");
      $upInsertTS = UserProfile::getUPByUserIdAndCode($user->id, "pw-validuntilTS");
      if (!$up || !$upInsertTS) {
        ResponseHelper::redirectAlertMessage(__("U heeft geen wachtwoord reset aangevraagd, of u heeft uw wachtwoord reeds ingesteld. Vraag uw wachtwoord opnieuw op via de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }
      if ($up->value != $_GET["hash"]) {
        ResponseHelper::redirectAlertMessage(__("Zorg dat u de complete link uit uw e-mail kopieert naar de browser."));
      }
      if (strtotime("-35 MINUTES") > $upInsertTS->value) {
        ResponseHelper::redirectAlertMessage(__("Uw wachtwoord link is verlopen. Vraag opnieuw uw wachtwoord op met de wachtwoord vergeten functionaliteit op de inloggen pagina."));
      }

      $errors = [];
      if (isset($_POST["go_password"])) {
        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }
        if (count($errors) == 0) {
          $user->password = $_POST["password1"];
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $user->password = User::encrypt($user->password);
          }
          $user->save();

          $up->destroy();
          $upInsertTS->destroy();

          MessageFlashCoordinator::addMessage(__("Uw wachtwoord is opgeslagen. U kunt nu inloggen."));
          ResponseHelper::redirect('/');
        }
      }

      $this->template = "passwordresetSuccess.php";
      $this->template_wrapper_only = true;
      $this->errors = $errors;
    }

    public function executeNewuser() {
      BreadCrumbs::getInstance()->removeLastItem();
      BreadCrumbs::getInstance()->addItem('Registreren');

      $errors = [];
      $user = new User();
      $organ = new Organisation();
      $payments = new OrganisationPayment();

      //default settings:
      $user->setUsergroup(User::USERGROUP_PARTICULIER);
      $organ->type = Organisation::TYPE_PARTICULIER;
      $organ->is_webshop_cust = true;

      $user->maylogin = 0;

      if (isset($_SESSION['userObject'])) {
        $user = User::find_by_id($_SESSION['userObject']->id);
        $organ = Organisation::find_by_id($_SESSION['userObject']->organisation_id);
        $payments = OrganisationPayment::getByOrganId($organ->id);
        if (!$payments) {
          $payments = new OrganisationPayment();
          $payments->organisation_id = $organ->id;
        }
      }

      if (isset($_POST['go'])) {
        if (isset($_POST['year_birthdate'])) {
          $user->birthdate = $_POST['year_birthdate'] . "-" . $_POST['month_birthdate'] . "-" . $_POST['day_birthdate'];
        }

        $user->sex = $_POST['sex'];
        $user->firstname = trim($_POST['firstname']);
        $user->insertion = trim($_POST['insertion']);
        $user->lastname = trim($_POST['lastname']);
        $user->phone = trim($_POST['phone']);
        if (isset($_POST['cellphone'])) $user->cellphone = trim($_POST['cellphone']);
        $user->email = trim($_POST['email']);
        if (Config::get('USER_SHOW_NEWSLETTER', true)) {
          $user->newsletter = isset($_POST['newsletter']) ? 1 : 0;
        }
        if (isset($_POST['passwordchange'])) {
          $user->password = trim($_POST['password1']);
          if (Config::isTrue("USER_PASSWORD_ENCRYPT")) {
            $user->password = User::encrypt($user->password);
          }
        }

        if (isset($_POST['organ_type'])) {
          $organ->type = $_POST['organ_type'];
          if ($organ->type == 'BEDRIJF') {
            $user->setUsergroup(User::USERGROUP_BEDRIJF);
          }
        }
        if (isset($_POST['organ_name'])) {
          $organ->name = trim($_POST['organ_name']);
        }


        $organ->address = trim($_POST['address']);
        $organ->number = trim($_POST['number']);
        $organ->zip = trim($_POST['zip']);
        $organ->city = trim($_POST['city']);
        $organ->country = $_POST['country'];

        if (isset($_POST['email_invoice'])) {
          if (ValidationHelper::isEmail($_POST['email_invoice'])) {
            $organ->email_invoice = trim($_POST['email_invoice']);
          }
          else {
            $organ->email_invoice = '';
          }
        }

        $organ->invoice_equal = 1;

        $organ->email = $_POST['email'];
        $organ->phone = $_POST['phone'];

        $organ->coc_number = isset($_POST['coc_number']) ? $_POST['coc_number'] : '';
        $organ->vat_number = isset($_POST['vat_number']) ? StringHelper::cleanVatnumber($_POST['vat_number']) : '';

        if (empty($user->sex)) {
          $errors['sex'] = __("Aanhef");
        }
        if (empty($user->lastname) || strlen($user->lastname) > 100) {
          $errors['lastname'] = __("Achternaam");
        }
        if (empty($user->phone) || strlen($user->phone) > 25) {
          $errors['phone'] = __("Telefoonnummer");
        }

        if ($organ->type == 'BEDRIJF') {
          if ($organ->name == "") {
            $errors['organ_name'] = __("Bedrijfsnaam");
          }
          if ($organ->vat_number != "" && !ValidationHelper::isVatnumber($organ->vat_number, $organ->country)) {
            $errors['vat_number'] = __("BTW-nummer");
          }
        }

        if ($organ->address == "") {
          $errors['address'] = __("Adres");
        }
        if ($organ->number == "") {
          $errors['number'] = __("Huisnummer");
        }
        if ($organ->zip == "") {
          $errors['zip'] = __("Postcode");
        }
        if ($organ->city == "") {
          $errors['city'] = __("Plaats");
        }
        if ($organ->country == "") {
          $errors['country'] = __("Land");
        }

        if ($user->email == "" || !ValidationHelper::isEmail($user->email)) {
          $errors['email'] = __("E-mailadres ontbreekt of is incorrect");
        }

        if (!User::isEmailUnique($user->email, '', $user->id)) {  //emailadres van bestelling frontend moet uniek zijn.
          $existing_user = User::find_by(['email' => $user->email]);
          if ($existing_user && $existing_user->maylogin == 1 && $existing_user->is_account == 1) {
            // gebruiker bestaat al en mag inloggen
            $errors['email'] = "Dit e-mailadres is reeds bekend in onze database. Bent u uw wachtwoord vergeten? Dan kunt u op de inlogpagina uw wachtwoord opnieuw opvragen.";
          }
          else {
            // gebruiker bestaat al, maar "mag inloggen" is uitgezet
            $errors['email'] = "Dit e-mailadres is reeds bekend in onze database als account, maar is nog niet geactiveerd.";
          }
        }

        if (isset($_POST['passwordchange'])) { //wachtwoord wordt gewijzigd / nieuwe gebruiker
          $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
          if ($passwordValid !== true) {
            $errors['password1'] = $passwordValid;
            $errors['password2'] = true;
          }
        }

        if (count($errors) == 0) {

          $site = $_SESSION['site'];

          if ($organ->id == "") {
            $organ->owner_organisation_id = $site->organisation_id;
            $organ->owner_user_id = Organisation::find_by_id($site->organisation_id)->getFirstUser()->id;
          }

          $changeditems['organisation'] = $organ->getChanged();
          $organ->save();

          $user->organisation_id = $organ->id;
          $changeditems['user'] = $user->getChanged();
          $user->save();

          if (defined('PAY_ONLINE_DEFAULT_ON') && PAY_ONLINE_DEFAULT_ON) $payments->pay_online = 1;
          if (PaymentMollie::isAvailable()) $payments->pay_online = 1;
          if (PAYMENT_OVERMAKEN_PART_ENABLED) $payments->overmaken = 1;
          $payments->organisation_id = $organ->id;
          $payments->save();

          $deliver_countries = Organisation::getDeliveryCountries();
          if (isset($deliver_countries[$organ->country])) {
            OrganisationAddress::updateDefaultAddressFromOrgan($organ, 'delivery', $user->getNaam());
          }

        }
        if (count($errors) == 0) {

          if (Config::get("USER_MAIL_ON_CHANGE", true)) {
            MailsFactory::sendEmailUserOrganChange($user, $changeditems);
          }

          $message = 'We hebben uw inschrijving succesvol ontvangen. Zodra uw account geactiveerd is zullen wij per email contact met u opnemen.';
          if (isset($_SESSION['redirectto']) && $_SESSION['redirectto'] != '') {
            $url = $_SESSION['redirectto'];
            unset($_SESSION['redirectto']);

            ResponseHelper::redirectMessage($message, $url);
          }
          else {
            ResponseHelper::redirectMessage($message);
          }
        }
      }

      $user->organisation = $organ;
      $this->user = $user;
      $this->errors = $errors;
      $this->seo_title = "Maak of wijzig uw account";
    }

    public function executeAddToBasketAjax() {
      $input = RequestHelper::getInputFileContents();
      $productId = $input->product_id;
      $quantity = $input->quantity;
      new BasketService()->addToBasket($productId, $quantity);
      ResponseHelper::exitAsJson([
        'success' => true,
        'message' => 'Product added to basket',
      ]);
    }

    public function executeAddToBasket() {
      $productId = $_POST['product_id'];
      $quantity = $_POST['quantity'] ?? 1;
      new BasketService()->addToBasket($productId, $quantity);
      ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
    }

    public function executeList() {
      $service = new BasketService();
      if (isset($_POST['delete_product_id'])) {
        $productId = $_POST['delete_product_id'];
        $service->removeFromBasket($productId);
      }
      $this->basket = $service->getBasket();;
    }

    public function executePay1() {
      $errors = [];
      $service = new BasketService();

      if (isset($_POST['select_address'])) {
        $addressId = $_POST['address_id'];
        $service->setShippingAddress($addressId);
        ResponseHelper::redirect(reconstructQueryAdd(['action' => 'pay2']));
      }

      if (isset($_POST['create_address'])) {
        $contactname = trim($_POST['contactname'] ?? '');
        $organisation_name = trim($_POST['organisation_name'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $number = trim($_POST['number'] ?? '');
        $zip = trim($_POST['zip'] ?? '');
        $city = trim($_POST['city'] ?? '');
        $country = $_POST['country'] ?? 'nl';

        // Validation
        if (empty($organisation_name)) {
          $errors['organisation_name'] = 'Bedrijfsnaam is verplicht';
        }
        if (empty($address)) {
          $errors['address'] = 'Adres is verplicht';
        }
        if (empty($number)) {
          $errors['number'] = 'Huisnummer is verplicht';
        }
        if (empty($zip)) {
          $errors['zip'] = 'Postcode is verplicht';
        }
        if (empty($city)) {
          $errors['city'] = 'Plaats is verplicht';
        }

        if (empty($errors)) {
          $newAddress = new OrganisationAddress();
          $newAddress->organisation_id = $_SESSION['userObject']->organisation->id;
          $newAddress->contactname = $contactname;
          $newAddress->organisation_name = $organisation_name;
          $newAddress->address = $address;
          $newAddress->number = $number;
          $newAddress->zip = $zip;
          $newAddress->city = $city;
          $newAddress->country = $country;
          $newAddress->type = 'delivery';

          $newAddress->save();
          $service->setShippingAddress($newAddress->id);
          ResponseHelper::redirect(reconstructQueryAdd(['action' => 'pay1', 'selected_address' => $newAddress->id]));
        }
      }

      $this->shippingaddresses = OrganisationAddress::getAddresses($_SESSION['userObject']->organisation->id);
      $this->errors = $errors;

      // set selected_address if it's set, otherwise set it to the default delivery address
      $this->selectedAddressId = $_GET['selected_address'] ?? array_find($this->shippingaddresses, fn ($address) => $address->is_default_delivery == 1)->id ?? null;
    }
  }