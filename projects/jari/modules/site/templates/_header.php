<header>
  <nav class="top-nav">
    <div class="top-container">
      <a href="/" title="logo" class="logo">
        <img src="<?php echo $site->getTemplateUrl() ?>images/jari-logo.svg" alt="logo">
      </a>
      <?php TemplateHelper::includeComponent('searchbar', 'site'); ?>
      <ul>
        <?php echo Navigation::getInstance()->writeSearchEngineNav($topMenuItems, true); ?>
        <?php if (!isset($_SESSION['userObject'])): ?>
          <li><a href="<?php echo PageMap::getUrl('M_BASKET') . '?action=pay0' ?>">Login</a></li>
        <?php else: ?>
          <li><a href="<?php echo PageMap::getUrl("M_LOGOFF") ?>">Uitloggen</a></li>
        <?php endif; ?>
        <li><a href="<?php echo PageMap::getUrl('M_BASKET') ?>">Winkelmandje</a></li>
      </ul>

      <div class="show-touch-devices">
        <button class="hamburger-button" aria-label="Open menu">
                    <span class="hamburger-button__inner">
                        <i></i>
                        <i></i>
                        <i></i>
                    </span>
        </button>
      </div>
    </div>
  </nav>

  <form action="<?php echo PageMap::getUrl("M_SEARCH"); ?>" method="post" id="search-form-mobile">
    <input id="search-input-mobile" name="search" type="search" placeholder="Zoeken...">
  </form>

  <?php if (ArrayHelper::hasData($categories)): ?>
    <nav class="bottom-nav">
      <ul class="bottom-container">
        <li class="dropdown">
          <a href="<?php echo PageMap::getUrl(6) ?>" title="Alle Producten">Alle Producten</a>
          <ul class="all-product-pages">
            <?php foreach ($categories as $category): ?>
              <?php /* @var Category $category */ ?>
              <li>
                <a href="<?php echo $category->getUrl($_SESSION['lang']) ?>" title="<?php echo $category->getSeoTitle($_SESSION['lang']) ?>">
                  <?php echo $category->getName($_SESSION['lang']) ?>
                </a>
              </li>
            <?php endforeach; ?>
          </ul>
        </li>
        <?php foreach ($bottomMenuItems as $bottomMenuItem): ?>
          <?php /* @var Category $bottomMenuItem */ ?>
          <li>
            <a href="<?php echo $bottomMenuItem->getUrl($_SESSION['lang']) ?>" title="<?php echo $bottomMenuItem->getSeoTitle($_SESSION['lang']) ?>">
              <?php echo $bottomMenuItem->getName($_SESSION['lang']) ?>
            </a>
          </li>
        <?php endforeach; ?>
      </ul>
    </nav>
  <?php endif; ?>
</header>

<div class="menubar">
  <div>
    <div class="dropdown">
      <a href="<?php echo PageMap::getUrl(6) ?>" title="Alle Producten">Alle Producten</a>
      <ul class="all-product-pages">
        <?php foreach ($categories as $category): ?>
          <?php /* @var Category $category */ ?>
          <li>
            <a href="<?php echo $category->getUrl($_SESSION['lang']) ?>" title="<?php echo $category->getSeoTitle($_SESSION['lang']) ?>">
              <?php echo $category->getName($_SESSION['lang']) ?>
            </a>
          </li>
        <?php endforeach; ?>
      </ul>
    </div>

    <ul class="product-pages">
      <?php foreach ($categories as $category): ?>
        <?php /* @var Category $category */ ?>
        <li>
          <a href="<?php echo $category->getUrl($_SESSION['lang']) ?>" title="<?php echo $category->getSeoTitle($_SESSION['lang']) ?>">
            <?php echo $category->getName($_SESSION['lang']) ?>
          </a>
        </li>
      <?php endforeach; ?>
    </ul>
    <ul class="other-pages">
      <?php echo Navigation::getInstance()->writeSearchEngineNav($topMenuItems, true); ?>
      <?php if (!isset($_SESSION['userObject'])): ?>
        <li><a href="<?php echo PageMap::getUrl('M_BASKET') . '?action=pay0' ?>">Login</a></li>
      <?php else: ?>
        <li><a href="<?php echo PageMap::getUrl("M_LOGOFF") ?>">Uitloggen</a></li>
      <?php endif; ?>
      <li>
        <a href="<?php echo PageMap::getUrl('M_BASKET') ?>">Winkelmandje</a>
      </li>
    </ul>
  </div>
</div>