<?php

  namespace domain\updater;

  use Gsd\Updater\GsdUpdater;

  /**
   * Class JariUpdater
   * Updaten van specifieke changes.
   * @package domain\updater
   */
  class JariUpdater extends GsdUpdater {
    protected function execute3(): bool {
      $query = <<<SQL
        CREATE TABLE `jari_order` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `abo_uid` VARCHAR(18) NULL,
          `user_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `user_id` (`user_id`),
          CONSTRAINT `fk_order_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      SQL;
      $this->executeQuery($query);

      $query = <<<SQL
        CREATE TABLE `jari_order_product` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `jari_order_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `product_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `size` DECIMAL(10, 2) NOT NULL,
          KEY `jari_order_id` (`jari_order_id`),
          KEY `product_id` (`product_id`),
          CONSTRAINT `fk_product_order_id` FOREIGN KEY (`jari_order_id`) REFERENCES `jari_order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT `fk_product_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      SQL;
      $this->executeQuery($query);
      return true;
    }

    protected function execute2(): bool {
      $query = <<<SQL
        CREATE TABLE `jari_product_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `product_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `product_id` (`product_id`),
          CONSTRAINT `fk_product_id` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($query);

      return true;
    }

    /**
     * 16-04-2025 - Jim - Link uid from ABO to category and to product container
     * @return bool
     */
    protected function execute1(): bool {
      $categoryquery = <<<SQL
        CREATE TABLE `jari_category_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `category_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `category_id` (`category_id`),
          CONSTRAINT `fk_category_id` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($categoryquery);

      $containerquery = <<<SQL
        CREATE TABLE `jari_product_container_uid` (
          `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
          `product_container_id` MEDIUMINT(8) UNSIGNED NOT NULL,
          `abo_uid` VARCHAR(18) NOT NULL UNIQUE,
          `insert_ts` DATETIME NOT NULL,
          `update_ts` DATETIME NOT NULL,
          KEY `product_container_id` (`product_container_id`),
          CONSTRAINT `fk_product_container_id` FOREIGN KEY (`product_container_id`) REFERENCES `product_cont` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
SQL;
      $this->executeQuery($containerquery);

      return true;
    }

//    /**
//     * [dd-mm-yyyy] - [Username] - [Description]
//     * @return bool
//     */
//    protected function execute123(): bool {
//      // run code
//      return true; // return true for succesfull increment
//    }

    public function __construct() {
      $this->setVersionCode(PROJECT . "-version");
    }

  }