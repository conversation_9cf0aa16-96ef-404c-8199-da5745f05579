<?php

  namespace domain\basket\service;

  use Product;
  use ProductCont;

  class BasketService {
    public function addToBasket($productId, $quantity) {
      $basket = $_SESSION['basket'] ?? ['items' => []];
      $existingKey = array_search($productId, array_column($basket['items'], 'product_id'));

      if ($existingKey !== false) {
        $basket['items'][$existingKey]['quantity'] += $quantity;
      } else if ($product = Product::getProductAndContent($productId)) {
        $product->container = ProductCont::find_by_id($product->product_cont_id);
        $product->container->setImageUrl();

        $basket['items'][] = [
          'product_id' => $productId,
          'product' => $product,
          'quantity' => $quantity,
        ];
      }
      $_SESSION['basket'] = $basket;
    }

    public function removeFromBasket($productId) {
      $basket = $_SESSION['basket'] ?? ['items' => []];
      $existingKey = array_search($productId, array_column($basket['items'], 'product_id'));
      if ($existingKey !== false) {
        unset($basket['items'][$existingKey]);
        $basket['items'] = array_values($basket['items']);
      }
      $_SESSION['basket'] = $basket;
    }

    public function setShippingAddress($addressId) {
      $_SESSION['basket']['shipping_address_id'] = $addressId;
    }

    public function getShippingAddress() {
      return $_SESSION['basket']['shipping_address_id'] ?? null;
    }

    public function getBasket() {
      return $_SESSION['basket'] ?? ['items' => []];
    }
  }